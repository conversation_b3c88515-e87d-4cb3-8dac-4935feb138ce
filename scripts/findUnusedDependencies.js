const path = require('path');
const fs = require('fs');
const { devDependencies, dependencies } = require('../package.json');

const excludeList = ['.cache', '.git', 'cicdDist', 'dist', 'node_modules'];
const projectDependencies = { ...devDependencies, ...dependencies };
const whiteList = [
  '@ky/line-build',
  '@typescript-eslint/eslint-plugin',
  'babel-plugin-import',
  'cross-env',
  'eslint-config-airbnb',
  'eslint-config-kyfe',
  'eslint-config-prettier',
  'eslint-plugin-import',
  'eslint-plugin-jsx-a11y',
  'eslint-plugin-prettier',
  'eslint-plugin-react',
  'husky',
  'lint-staged',
  'node-sass',
  'prettier-eslint',
  'webpack-cli',
  'webpack-dev-server',
  '@babel/runtime',
  'core-js',
  '@babel/core',
  'lottie-web',
  'process',
];

// 执行
const unUsed = findUnusedDependencies();
console.log('未使用的包', unUsed);
console.log('npm uninstall ' + unUsed.join(' '));

/**
 * 查找项目中未使用的依赖包
 */
function findUnusedDependencies() {
  const fileList = findAllFile(path.resolve(__dirname, '../'));
  fileList.forEach(item => {
    tagUsedPackage(item);
  });

  const unUsedPackages = [];
  for (let key in projectDependencies) {
    if (
      typeof projectDependencies[key] === 'string' &&
      projectDependencies[key.replace('@types/', '')] !== true &&
      !whiteList.includes(key)
    ) {
      unUsedPackages.push(key);
    }
  }

  return unUsedPackages;
}

/**
 * 查找所有文件
 * @param {string} filePath 文件路径
 * @param {string[]} queue 文件路径数组
 */
function findAllFile(filePath, queue = []) {
  if (excludeList.some(item => filePath.includes(item))) {
    return queue;
  }

  const data = fs.statSync(filePath);
  if (data.isFile()) {
    if (/\.[tj]sx?$/.test(filePath)) {
      queue.push(filePath);
    }
    return queue;
  }

  const list = fs.readdirSync(filePath);
  list.forEach(item => {
    findAllFile(`${filePath}/${item}`, queue);
  });
  return queue;
}

/**
 * 标记已使用的包
 * @param {string}} filePath 文件路径
 */
function tagUsedPackage(filePath) {
  const content = fs
    .readFileSync(filePath, { encoding: 'utf-8' })
    .replace(/\s{2,}/g, ' ');
  Object.entries(projectDependencies).forEach(([package, hasUsed]) => {
    if (typeof hasUsed === 'boolean') {
      return;
    }
    if (
      new RegExp(`(\\b|@)${package.replace('@', '')}\\b`, 'g').test(content)
    ) {
      projectDependencies[package] = true;
    }
  });
}
