# ResourceServices 接口使用文档

## 概述
本文档列出了项目中所有使用 `resourceServices` 的接口，包括接口路径、使用页面、功能描述等信息，方便进行迁移和测试。

## 接口列表

### 1. 货量预测相关接口

#### 1.1 货量预测数据
- **接口路径**: `/resourceServices/report/oneDimen/tb_transfer_forecasting_result`
- **方法**: POST
- **函数名**: `getForecastData`
- **使用页面**: 
  - 异常监控页面 (`src/containers/newHome/ExceptionWarning/index.js`)
- **功能**: 获取货量预测数据
- **路由**: `/newHome/exceptionMonitor`

#### 1.2 货量预测（线路盈亏测算）
- **接口路径**: `/resourceServices/report/twoDimen/ads_fin_cargo_forecast_result_sum_di`
- **方法**: POST
- **函数名**: `queryCargoForecast`
- **使用页面**: 
  - 线路盈亏测算页面 (`src/containers/LineProfit/index.js`)
- **功能**: 获取货量预测数据用于线路盈亏分析
- **路由**: `/lineProfit`

#### 1.3 盈亏货量测算
- **接口路径**: `/resourceServices/report/twoDimen/ads_fin_cargo_forecast_sum_di`
- **方法**: POST
- **函数名**: `queryWeightRate`
- **使用页面**: 
  - 线路盈亏测算页面 - 盈亏货量测算组件 (`src/containers/LineProfit/StockCalc/index.js`)
- **功能**: 计算盈亏货量测算数据
- **路由**: `/lineProfit`

#### 1.4 线路利润预测
- **接口路径**: 
  - 日维度: `/resourceServices/report/twoDimen/ads_fin_cargo_forecast_flow_sum_di`
  - 月维度: `/resourceServices/report/twoDimen/ads_fin_profit_flow_mon_sum_mi`
- **方法**: POST
- **函数名**: `queryFlowForecast`
- **使用页面**: 
  - 线路盈亏测算页面 - 线路利润组件 (`src/containers/LineProfit/FlowCalc/index.js`)
- **功能**: 获取线路利润预测数据
- **路由**: `/lineProfit`

### 2. 人员效能相关接口

#### 2.1 人员效能数据
- **接口路径**: `/resourceServices/report/oneDimen/he_kg_result_data`
- **方法**: POST
- **函数名**: `getEffectyData`
- **使用页面**: 
  - 效率概览页面 (`src/containers/efficiencyDetail/index.js`)
  - 异常监控页面 (`src/containers/newHome/ExceptionWarning/index.js`)
- **功能**: 获取人员效能数据
- **路由**: `/efficiencyDetail`, `/newHome/exceptionMonitor`

#### 2.2 站叉、坐叉效率数据
- **接口路径**: `/resourceServices/report/twoDimen/tp_forklift_efficiency_rpt`
- **方法**: POST
- **函数名**: `getEfficiencyData`
- **使用页面**: 
  - 人员效能相关页面
- **功能**: 获取站叉、坐叉效率数据
- **路由**: 人员效能相关页面

#### 2.3 站叉、坐叉平均效率数据
- **接口路径**: `/resourceServices/report/twoDimen/tp_forklift_efficiency_rpt1`
- **方法**: POST
- **函数名**: `getAverageEfficiencyData`
- **使用页面**: 
  - 人员效能相关页面
- **功能**: 获取站叉、坐叉平均效率数据
- **路由**: 人员效能相关页面

### 3. 个人计提相关接口

#### 3.1 个人叉车计量数据
- **接口路径**: `/resourceServices/report/oneDimen/tp_forklift_efficiency_rpt_s`
- **方法**: POST
- **函数名**: `queryPersonalLift`
- **使用页面**: 
  - 个人计提页面 (`src/containers/AccureBoard/index.js`)
- **功能**: 查询当月和所选日期的叉车计量数据
- **路由**: `/accureBoard`

#### 3.2 个人叉车计量详情数据
- **接口路径**: `/resourceServices/report/twoDimen/tp_forklift_efficiency_detail`
- **方法**: POST
- **函数名**: `queryPersonalLiftDetail`
- **使用页面**: 
  - 个人计提页面 (`src/containers/AccureBoard/index.js`)
- **功能**: 查询叉车计量详情数据
- **路由**: `/accureBoard`

### 4. 质量管理相关接口

#### 4.1 管理看板质量数据（场地）
- **接口路径**: `/resourceServices/report/oneDimen/kg_charge_person_trans_result_data`
- **方法**: POST
- **函数名**: `getManageQualityData`
- **使用页面**: 
  - 管理看板首页
- **功能**: 获取质量环节场地数据
- **路由**: 管理看板相关页面

#### 4.2 管理看板质量数据（班组）
- **接口路径**: `/resourceServices/report/twoDimen/kg_charge_person_duty_result_dtl`
- **方法**: POST
- **函数名**: `getManageQualityBatch`
- **使用页面**: 
  - 管理看板首页
- **功能**: 获取质量环节班组数据
- **路由**: 管理看板相关页面

#### 4.3 装车经理质量数据
- **接口路径**: `/resourceServices/report/oneDimen/kg_charge_person_duty_result_dtl2`
- **方法**: POST
- **函数名**: `getLoadManageDutyData`
- **使用页面**: 
  - 装车经理页面
- **功能**: 获取装车质量数据概览
- **路由**: 装车经理相关页面

#### 4.4 卸车负责人质量数据（场地）
- **接口路径**: `/resourceServices/report/oneDimen/kg_charge_person_duty_result_dtl3`
- **方法**: POST
- **函数名**: `getUnloadManageDutyData`
- **使用页面**: 
  - 卸车负责人界面
- **功能**: 获取卸车质量环节场地汇总数据
- **路由**: 卸车负责人相关页面

#### 4.5 卸车负责人质量数据（班组）
- **接口路径**: `/resourceServices/report/twoDimen/kg_charge_person_duty_result_dtl5`
- **方法**: POST
- **函数名**: `getUnloadManageBatchData`
- **使用页面**: 
  - 卸车负责人界面
- **功能**: 获取卸车质量环节班组数据
- **路由**: 卸车负责人相关页面

### 5. 品质详情相关接口

#### 5.1 客诉数据
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_detail_complain`
- **方法**: POST
- **函数名**: `getQualityComplaintData`
- **使用页面**: 
  - 品质概览页面 (`src/containers/qualityBoard/index.js`)
- **功能**: 获取今日新增客诉数据
- **路由**: `/qualityBoard`

#### 5.2 遗失数据
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_detail_lost`
- **方法**: POST
- **函数名**: `getQualityLostData`
- **使用页面**: 
  - 品质概览页面 (`src/containers/qualityBoard/index.js`)
- **功能**: 获取今日新增遗失数据
- **路由**: `/qualityBoard`

#### 5.3 损坏数据
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_detail_damage`
- **方法**: POST
- **函数名**: `getQualityDamageData`
- **使用页面**: 
  - 品质概览页面 (`src/containers/qualityBoard/index.js`)
- **功能**: 获取今日新增损坏数据
- **路由**: `/qualityBoard`

#### 5.4 质量汇总数据
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_detail_total`
- **方法**: POST
- **函数名**: `getQualityTotalData`
- **使用页面**: 
  - 品质概览页面 (`src/containers/qualityBoard/index.js`)
  - 管理看板汇总页面 (`src/containers/newHome/NewManageBoard/Summary/index.js`)
- **功能**: 获取客诉/损坏/遗失汇总数据
- **路由**: `/qualityBoard`, 管理看板相关页面

#### 5.5 客诉定责明细
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_detail_complain_total`
- **方法**: POST
- **函数名**: `getQualityComplaintDetail`
- **使用页面**: 
  - 品质概览页面
- **功能**: 获取当月客诉定责明细数据
- **路由**: `/qualityBoard`

#### 5.6 损坏定责明细
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_detail_damage_total`
- **方法**: POST
- **函数名**: `getQualityDamageDetail`
- **使用页面**: 
  - 品质概览页面
- **功能**: 获取当月损坏定责明细数据
- **路由**: `/qualityBoard`

#### 5.7 遗失定责明细
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_lost_gather`
- **方法**: POST
- **函数名**: `getQualityLostDetail`
- **使用页面**: 
  - 品质概览页面
- **功能**: 获取当月遗失定责明细数据
- **路由**: `/qualityBoard`

### 6. 排行榜相关接口

#### 6.1 中转场客诉排名
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_data_department_complain`
- **方法**: POST
- **函数名**: `queryDepartComplainRankData`
- **使用页面**: 
  - 排行榜页面 (`src/containers/RankBoard/index.js`)
- **功能**: 获取中转场客诉票数排名
- **路由**: `/rankBoard`

#### 6.2 中转场损坏排名
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_data_department_damage`
- **方法**: POST
- **函数名**: `queryDepartDamageRankData`
- **使用页面**: 
  - 排行榜页面 (`src/containers/RankBoard/index.js`)
- **功能**: 获取中转场损坏票数排名
- **路由**: `/rankBoard`

#### 6.3 中转场遗失排名
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_data_department_lost`
- **方法**: POST
- **函数名**: `queryDepartLostRankData`
- **使用页面**: 
  - 排行榜页面 (`src/containers/RankBoard/index.js`)
- **功能**: 获取中转场遗失票数排名
- **路由**: `/rankBoard`

#### 6.4 分拨区客诉排名
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_data_complain`
- **方法**: POST
- **函数名**: `queryDistributeComplainRank`
- **使用页面**: 
  - 排行榜页面 (`src/containers/RankBoard/index.js`)
- **功能**: 获取分拨区客诉票数排名
- **路由**: `/rankBoard`

#### 6.5 分拨区损坏排名
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_data_damage`
- **方法**: POST
- **函数名**: `queryDistributeDamageRank`
- **使用页面**: 
  - 排行榜页面 (`src/containers/RankBoard/index.js`)
- **功能**: 获取分拨区损坏票数排名
- **路由**: `/rankBoard`

#### 6.6 分拨区遗失排名
- **接口路径**: `/resourceServices/report/twoDimen/opqm_kg_result_data_lost`
- **方法**: POST
- **函数名**: `queryDistributeLostRank`
- **使用页面**: 
  - 排行榜页面 (`src/containers/RankBoard/index.js`)
- **功能**: 获取分拨区遗失票数排名
- **路由**: `/rankBoard`

## 迁移注意事项

1. **接口依赖**: 所有接口都依赖 `resourceServices` 服务，迁移时需要确保新的服务地址配置正确
2. **数据格式**: 大部分接口返回的数据格式为 `{ colDefList: [], rows: [] }`，需要在前端进行数据转换
3. **参数格式**: 大部分接口使用 `conditionList` 数组格式传递查询条件
4. **错误处理**: 各页面对接口错误的处理方式不同，迁移时需要保持一致性
5. **缓存策略**: 部分页面有数据缓存逻辑，迁移时需要考虑缓存失效问题

## 测试建议

1. **功能测试**: 按页面逐一测试各接口功能是否正常
2. **数据验证**: 验证接口返回数据的准确性和完整性
3. **性能测试**: 测试接口响应时间是否满足要求
4. **异常测试**: 测试网络异常、服务异常等情况下的处理
5. **兼容性测试**: 确保新旧接口数据格式兼容

## 相关文件

- **接口定义**: `src/actions/index.js`
- **代理配置**: `src/proxyList.js` (第18行，resourceServices 被注释)
- **路由配置**: `src/components/Loading/titleMap.ts`
