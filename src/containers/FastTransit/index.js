import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { PullToRefresh } from 'antd-mobile';
import { getKzParam, queryMustGo, queryPickUp } from 'src/actions';
import './index.scss';

export default function() {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const type = queryParams.get('type');
  const title = type === '0' ? '必走货明细' : '应提明细';
  const dataId = queryParams.get('dataId');
  const pageSize = 20;
  document.title = title;
  const [param, setParam] = useState({});

  const [page, setPage] = useState(1);
  const [list, setList] = useState([]);
  const [total, setTotal] = useState(0);


  function queryDataList(req) {
    if (type === '0') {
      queryMustGo(req).then(res => {
        console.log('queryMustGo = ', res);
        const dataList = res?.hits?.hits || [];
        setTotal(res?.hits?.total || 0);
        let newDataList = dataList.map(item => ({
          mainWaybillNo: item.source.main_waybill_no,
          nextSiteName: item.source.next_site_name,
          count: item.source.pack_count,
          weight: item.source.weight,
          vol: item.source.vol,
          key: item.id,
        }));
        setList([...list, ...newDataList]);
      });
    } else {
      queryPickUp(req).then(res => {
        console.log('queryPickUp = ', res);
        const dataList = res?.hits?.hits || [];
        setTotal(res?.hits?.total || 0);
        let newDataList = dataList.map(item => ({
          mainWaybillNo: item.source.pWaybillNo,
          nextSiteName: item.source.outNetOrgName,
          count: item.source.quantity,
          weight: item.source.actualWeight,
          vol: item.source.volume,
          key: item.id,
        }));
        setList([...list, ...newDataList]);

      });
    }
  }

  function fetchDataList() {
    if (page * pageSize >= total) {
      return;
    }
    let nextPage = page + 1;
    setPage(nextPage);
    queryDataList({ zonecode: param.zonecode, nextZoneCode: param.nextZoneCode, nextPage, pageSize });
  }

  useEffect(() => {
    const initLoadData = async () => {
      const res = await getKzParam({ dataId });
      let newParam = JSON.parse(res);
      console.log(newParam);
      setParam(newParam);
      queryDataList({ zonecode: newParam.zonecode, nextZoneCode: newParam.nextZoneCode, page, pageSize });
    };
    initLoadData();

  }, []);


  return (
    <>
      <div className="BiZhouHuaTable-root">
        <div className="table-header">
          <div className="header-row">
            <span className="header-cell">运单号</span>
            <span className="header-cell">下一站</span>
            <span className="header-cell font-style">货物明细</span>
          </div>
        </div>

        <div className="table-body">
          <PullToRefresh
            direction="up"
            onRefresh={fetchDataList}
          >
            {
              list.map(item => (
                <div className="body-row" key={item.key}>
                  <span className="body-cell">{item.mainWaybillNo}</span>
                  <span className="body-cell">{item.nextSiteName?.replace('【SX】', '')}</span>
                  <span
                    className="body-cell">{(item.count || 0)}件/{(item.weight || 0).toFixed(0)}kg/{(item.vol || 0).toFixed(2)}m²</span>
                </div>
              ))
            }
          </PullToRefresh>
        </div>

      </div>
    </>
  );

}
