import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import dayjs from 'dayjs';
import axios from 'src/utils/axios';
import { DateSelect } from 'src/components/DateSelect';
import Header from '../../components/Header';
import { REFRESH_STATE, LOAD_STATE } from '../../constants';
import TableWithHeader from '../../components/TableWithHeader';
import './index.scss';

// 数据格式转换函数 - 日模式
function convertDataToTableFormatDay(data) {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => [
    item.loginid || '-', // 员工编码
    item.username || '-', // 员工姓名
    item.positionName || '-', // 岗位
    Number(item.totalWeight || 0).toFixed(1), // 当日总货量 (吨)
    `${Number(item.weekTotalWeightRatio || 0).toFixed(1)}%`, // 周货量同比
    `${Number(item.totalWeightRatio || 0).toFixed(1)}%`, // 货量日环比
  ]);
}

// 数据格式转换函数 - 月模式
function convertDataToTableFormatMonth(data) {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => [
    item.loginid || '-', // 员工编码
    item.username || '-', // 员工姓名
    item.positionName || '-', // 岗位
    Number(item.totalWeightMtd || 0).toFixed(1), // 当月总货量
    `${Number(item.totalWeightMtdRatio || 0).toFixed(1)}%`, // 货量月环比
    Number(item.overweightInctMtd || 0), // 当月偷重偷方上报票数
    Number(item.attendanceDays || 0), // 当月出勤天数
    Number(item.effMtd || 0).toFixed(1), // 当月效能
  ]);
}

// 装卸组别详情列表页面
class GroupPersonnelDetail extends React.PureComponent {
  constructor(props) {
    super(props);

    // 从URL查询参数获取传递的日期
    const urlParams = new URLSearchParams(props.location.search);
    const dateParam = urlParams.get('date');
    let initialDate = dayjs()
      .subtract(1, 'day')
      .toDate(); // 默认昨天

    if (dateParam) {
      // 如果URL中有日期参数，使用该日期
      const parsedDate = dayjs(dateParam);
      if (parsedDate.isValid()) {
        initialDate = parsedDate.toDate();
      }
    }

    this.state = {
      selectedDate: initialDate,
    };

    this.loadGroupPersonnelDetail = this.loadGroupPersonnelDetail.bind(this);
    this.refreshGroupPersonnelDetail = this.refreshGroupPersonnelDetail.bind(
      this,
    );
    this.handleDateChange = this.handleDateChange.bind(this);

    // 用于存储TableWithHeader的状态管理函数
    this.tableStateHandlers = null;
  }

  async componentDidMount() {
    // 初始化数据加载
  }

  // 从URL参数获取组别名称
  getGroupNameFromParams() {
    const { match } = this.props;
    return match?.params?.groupName || '';
  }

  // 从URL参数获取type参数
  getTypeFromParams() {
    const { match } = this.props;
    const type = match?.params?.type;
    return type ? parseInt(type, 10) : 0; // 默认为日维度(0)
  }

  // 日期变更处理
  handleDateChange(date) {
    // 检查选择的日期是否为今天或之后，如果是则不允许选择
    const today = dayjs().startOf('day');
    const selectedDay = dayjs(date).startOf('day');

    if (selectedDay.isSame(today) || selectedDay.isAfter(today)) {
      // 如果选择的是今天或之后的日期，显示提示并不更新状态
      return;
    }

    this.setState(
      {
        selectedDate: date,
      },
      () => {
        // 日期变更后触发数据重新加载
        // 只设置 currentPage 为 0，让 TableWithHeader 自动触发 refresh
        if (this.tableStateHandlers) {
          this.tableStateHandlers.setCurrentPage(0);
        }
      },
    );
  }

  // 获取装卸组别详情数据
  async fetchGroupPersonnelDetailData() {
    const { zoneCode } = this.props;
    const { selectedDate } = this.state;
    const groupName = this.getGroupNameFromParams();
    const type = this.getTypeFromParams();

    try {
      // 构造请求参数 - 获取特定组别的详情数据
      // 根据type参数格式化日期：日维度用完整日期，月维度用月初日期
      const formattedDate = dayjs(selectedDate).format(
        type === 1 ? 'YYYY-MM-01 00:00:00' : 'YYYY-MM-DD 00:00:00',
      );

      const response = await axios({
        url: '/cockpit/kg/personEff/getPersonEffGroupDetail',
        method: 'POST',
        data: {
          date: formattedDate,
          deptCode: zoneCode,
          orgName: groupName,
          type, // 添加type参数：0:日维度,1:月维度
          // 移除分页参数，因为接口不需要分页功能
        },
      });

      return response;
    } catch (error) {
      // 获取装卸组别详情数据失败
      throw error;
    }
  }

  // 下拉刷新数据
  refreshGroupPersonnelDetail({
    setCurrentPage,
    setRefreshing,
    setDatas,
    setLoading,
  }) {
    // 存储状态管理函数，供日期变更时使用
    this.tableStateHandlers = {
      setCurrentPage,
      setRefreshing,
      setDatas,
      setLoading,
    };

    setRefreshing(REFRESH_STATE.loading);
    const type = this.getTypeFromParams();
    const isMonthMode = type === 1;

    this.fetchGroupPersonnelDetailData()
      .then(res => {
        setCurrentPage(1);
        // 根据模式选择对应的数据转换函数
        const formattedData = isMonthMode
          ? convertDataToTableFormatMonth(res.rows || res || [])
          : convertDataToTableFormatDay(res.rows || res || []);
        setDatas(formattedData);
        setRefreshing(REFRESH_STATE.success);
      })
      .catch(() => {
        setRefreshing(REFRESH_STATE.failure);
      });
  }

  // 上拉加载更多数据
  // 注意：此接口不需要分页功能，所以直接设置为完成状态，禁用触底加载
  loadGroupPersonnelDetail({ setLoading }) {
    // 存储状态管理函数，供日期变更时使用
    if (!this.tableStateHandlers) {
      this.tableStateHandlers = {
        setLoading,
      };
    }

    // 由于接口不需要分页，直接设置为完成状态，避免无限触底加载
    setLoading(LOAD_STATE.complete);
  }

  render() {
    const { selectedDate } = this.state;
    const { refreshGroupPersonnelDetail, loadGroupPersonnelDetail } = this;
    const groupName = decodeURIComponent(this.getGroupNameFromParams());
    const type = this.getTypeFromParams();

    // 根据type参数确定时间维度
    const isMonthMode = type === 1;
    const timeLabel = isMonthMode ? '月累计人效比' : '日累计人效比';

    // 根据模式配置不同的表头和列宽
    const headers = isMonthMode
      ? [
          '员工编码',
          '员工姓名',
          '岗位',
          '当月总货量',
          '货量月环比',
          '当月偷重偷方上报票数',
          '当月出勤天数',
          '当月效能',
        ]
      : [
          '员工编码',
          '员工姓名',
          '岗位',
          '当日总货量 (吨)',
          '周货量同比',
          '货量日环比',
        ];

    // 根据模式配置不同的列宽
    const columnWidths = isMonthMode
      ? [
          70, // 员工编码
          70, // 员工姓名
          100, // 岗位
          80, // 当月总货量
          80, // 货量月环比
          130, // 当月偷重偷方上报票数 - 增加宽度
          80, // 当月出勤天数
          70, // 当月效能
        ]
      : [
          70, // 员工编码
          70, // 员工姓名
          100, // 岗位
          90, // 当日总货量 (吨)
          80, // 周货量同比
          80, // 货量日环比
        ];

    return (
      <div className="group-personnel-detail-wrap">
        <Header {...this.props} title={`${groupName}详情`} noFilter />

        {/* 日期选择器区域 */}
        <div className="date-selector-section">
          <div className="section-title">{timeLabel} (T-1)</div>
          <div className="date-selector">
            <DateSelect
              mode={isMonthMode ? 'month' : 'date'}
              value={selectedDate}
              onChange={this.handleDateChange}
              placeholder={isMonthMode ? '选择月份' : '选择日期'}
              format={isMonthMode ? 'YYYY-MM' : 'YYYY-MM-DD'}
              maxDate={dayjs()
                .subtract(1, 'day')
                .toDate()}
            />
          </div>
        </div>

        <div className="table-container">
          <TableWithHeader
            headers={headers}
            columnWidths={columnWidths}
            dataSource={[]}
            refreshing={0}
            loading={0}
            load={loadGroupPersonnelDetail}
            refresh={refreshGroupPersonnelDetail}
            className="group-personnel-table"
          />
        </div>
      </div>
    );
  }
}

function mapStateToProps(state) {
  return {
    batchCode: state.global.batchCode,
    zoneCode: state.global.zoneCode,
    planBeginTm: state.global.planBeginTm,
    workDate: state.global.workDate,
  };
}

export default connect(mapStateToProps)(withRouter(GroupPersonnelDetail));
