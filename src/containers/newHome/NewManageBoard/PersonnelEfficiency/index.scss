.PersonnelEfficiency-root {
  padding: 10px;
  background: #ffffff;
  // background: linear-gradient(180deg, #f5f0e8 0%, #fdfcf8 100%);
  min-height: 100vh;
  border-radius: 10px;
  .chart-container {
    margin: 10px 0px;
    height: 140px;
    // padding: 0 10px;
    width: 324px;
    // background-color: red;

    // 确保图表容器有足够的空间显示Y轴标签
    .echart-container {
      width: 100% !important;
      height: 100% !important;

      // 为Y轴标签预留更多空间
      canvas {
        margin-left: 5px;
      }
    }
  }
  // .middle-container {
  //   padding: 0 10px;
  // }
  .view-more-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 35px;
    background-color: #f0f3f9;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    margin-top: 10px;
    border-radius: 4px;
    // margin: 0 10px;
    cursor: pointer;

    .arrow {
      margin-left: 4px;
    }
  }
  .header-title {
    // margin-left: 10px;
    // padding-top: 10px;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 600;
  }

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin: 0 16px;
    padding-top: 8px;
    align-items: center; // Ensure vertical alignment

    .time-granularity-switcher {
      display: flex;
      height: 24px;
      border-radius: 12px;
      background-color: #f5f5f5;

      .granularity-btn {
        padding: 0 14px;
        line-height: 24px;
        border: none;
        color: #606266;
        font-size: 14px;
        border-radius: 12px;
        background-color: inherit;

        &.active {
          background-color: #ffffff;
          color: #cc1b23;
          border-radius: 12px;
          border: 0.5px solid #e7e7e7;
        }

        &:hover:not(.active) {
          // background-color: #f5f5f5;
        }
      }
    }

    .LineTitle-root {
      // margin: 16px 0 8px 0;
      flex: 1;

      .title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
    }

    .date-selector {
      padding: 8px 0 8px 12px;
      font-size: 14px;
      color: #333;
      font-weight: normal;
      font-family: PingFang SC;

      border: 1px solid rgba(255, 255, 255, 0.2);

      .placeholder {
        color: #999;
      }
    }
  }

  .LineChart-root {
    // margin: 12px 16px;
    // background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    // padding: 20px;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);

    .top-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 0 0 16px 0;
    }

    .tooltip {
      margin-bottom: 12px;

      &__content {
        display: flex;
        align-items: center;
        margin: 4px 0;
        font-size: 12px;
        color: #666;

        i {
          display: inline-block;
          width: 8px;
          height: 8px;
          margin-right: 8px;
          border-radius: 2px;

          &.radio {
            border-radius: 50%;
          }

          &.square {
            border-radius: 2px;
          }
        }

        span {
          margin-left: 8px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .canvas-container {
      position: relative;
      height: 200px;
      background: rgba(248, 250, 252, 0.5);
      border-radius: 8px;
      padding: 8px;

      canvas {
        width: 100%;
        height: 100%;
      }
    }
  }

  .LineTitle-root {
    // margin: 20px 16px 16px 16px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
      white-space: nowrap;
    }
  }
}

.PersonnelEfficiency-root {
  .header-section {
    // margin: 0 10px;

    .date-selector {
      // padding: 6px 10px;
      font-size: 13px;
    }
  }

  .LineChart-root {
    // margin: 12px 12px;
    // padding: 16px 12px;
  }

  .LineTitle-root {
    // margin: 16px 12px 12px 12px;
  }

  // 各组别累计人数比例样式
  .group-personnel-ratio {
    .group-table-container {
      // margin: 10px;
      border-radius: 8px;
      // padding: 10px;

      .group-table-header {
        background-color: #f0f3f9;
        display: flex;
        padding: 8px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        font-size: 12px;
        font-weight: 600;
        color: #999999;

        .header-cell {
          flex: 1;
          text-align: center;
          font-size: 11px;

          &:first-child {
            text-align: left;
            padding-left: 10px;
          }
        }
      }

      .group-table-body {
        .table-row {
          display: flex;
          padding: 12px 0;
          border-bottom: 1px solid rgba(0, 0, 0, 0.03);
          font-size: 12px;
          color: #333;

          .table-cell {
            flex: 1;
            text-align: center;

            &:first-child {
              text-align: left;
              padding-left: 10px;
              font-weight: 500;
            }

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }

      .view-more-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 35px;
        background-color: #f0f3f9;
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 500;

        .view-more-btn {
          .arrow {
            margin-left: 4px;
            font-size: 16px;
          }
        }
        .arrow {
          margin-left: 4px;
        }
      }
    }
  }
  .view-more-container-arrow {
    width: 12px;
    height: 12px;
    margin-left: 4px;
  }
}
