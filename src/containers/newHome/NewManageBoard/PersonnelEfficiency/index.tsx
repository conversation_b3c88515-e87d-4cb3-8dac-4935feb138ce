import React, { useReducer, useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { connect } from 'react-redux';
import dayjs from 'dayjs';
import axios from 'src/utils/axios';
import './index.scss';
import { useConditionEffect } from '../useConditionEffect';
import { TAB_INDICES } from '../constants';
import { EfficiencyCards } from './EfficiencyCards';
import { GroupPersonnelRatio } from './GroupPersonnelRatio';
import EchartComponent from './components/echart';
import PersonnelEfficiencyHeader from './components/PersonnelEfficiencyHeader';
import LaborEfficiencyAbnormal from './components/LaborEfficiencyAbnormal';
import arrowImg from '../images/arrow.svg';

/**
 * 时间粒度类型枚举
 * @readonly
 * @enum {string}
 */
const GranularityType = {
  /** 日维度 */
  DAY: 'date',
  /** 月维度 */
  MONTH: 'month',
};

const PersonnelEfficiencyComp = ({ zoneCode }) => {
  const history = useHistory();
  const [selectedDate, setSelectedDate] = useState(
    new Date(Date.now() - 86400000),
  ); // 设置默认时间为前一天(t-1)
  const [timeGranularity, setTimeGranularity] = useState(GranularityType.DAY); // 使用枚举替代字符串

  const [data, dispatchData] = useReducer(
    (state, actions) => ({ ...state, ...actions.payload }),
    {},
  );

  const { efficiencyCards = {}, loading = false } = data;

  // 获取人员效能趋势数据
  const fetchPersonnelEfficiencyData = async (granularity, date) => {
    dispatchData({ payload: { loading: true } });

    try {
      const type = granularity === GranularityType.DAY ? 0 : 1; // 0:日维度, 1:月维度
      const formattedDate = dayjs(date).format(
        granularity === GranularityType.DAY
          ? 'YYYY-MM-DD 00:00:00'
          : 'YYYY-MM-01 00:00:00',
      );

      // http://yapi.sit.sf-express.com/project/4230/interface/api/cat_108157
      // 调用真实 API 接口
      const response = await axios({
        url: '/cockpit/kg/personEff/trend',
        method: 'POST',
        data: {
          date: formattedDate,
          type,
          deptCode: zoneCode, // 使用从全局状态获取的 zoneCode 作为 deptCode
        },
      });
      // 处理接口返回的数据
      if (response) {
        // 转换接口返回的数据结构为组件所需格式
        const apiEfficiencyCards = {
          opWeight: response?.opWeight || 0,
          opAttendanceEff: response?.opAttendanceEff || 0,
          mobileAttendanceEff: response?.mobileAttendanceEff || 0,
          laborEff: response?.laborEff || 0,
          weightRatio: response?.weightRatio || 0,
          // 添加其他需要的字段
          opAttendanceNumber: response?.opOwnOnWork || 0,
          mobileAttendanceNumber: response?.mobileOwnOnWork || 0,
          // 添加数据类型标识，用于子组件判断数据是否匹配当前时间粒度
          dataType: type === 1 ? 'month' : 'date',
        };
        if (type === 1) {
          apiEfficiencyCards.opWeight = response?.monthOpWeight || 0;
          apiEfficiencyCards.opAttendanceEff =
            response?.monthOpAttendanceEff || 0;
          apiEfficiencyCards.mobileAttendanceEff =
            response?.monthMobileAttendanceEff || 0;
          apiEfficiencyCards.laborEff = response?.monthLaborEff || 0;
          apiEfficiencyCards.weightRatio = response?.monthWeightRatio || 0;
        }

        dispatchData({
          payload: {
            efficiencyCards: apiEfficiencyCards,
            loading: false,
          },
        });
      }

      // 同时获取趋势图表数据
      await fetchPersonnelEfficiencyTrendChart(granularity, date);
    } catch (error) {
      console.error('获取人员效能数据失败:', error);

      // 异常情况下使用 mock 数据作为降级方案
      // try {
      //   const mockResult = await getPersonnelEfficiencyData(
      //     dayjs(date).format(granularity === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM'),
      //     granularity === 'date' ? 0 : 1,
      //     zoneCode,
      //   );

      //   dispatchData({
      //     payload: {
      //       ...mockResult.data,
      //       loading: false,
      //     },
      //   });
      // } catch (mockError) {
      //   dispatchData({ payload: { loading: false } });
      // }
    }
  };

  // 获取人员效能趋势图表数据
  const fetchPersonnelEfficiencyTrendChart = async (granularity, date) => {
    try {
      const type = granularity === GranularityType.DAY ? 0 : 1; // 0:日维度, 1:月维度
      const formattedDate = dayjs(date).format(
        granularity === GranularityType.DAY
          ? 'YYYY-MM-DD 00:00:00'
          : 'YYYY-MM-01 00:00:00',
      );

      // 调用新增的趋势图表接口
      const response = await axios({
        url: '/cockpit/kg/personEff/trendChart',
        method: 'POST',
        data: {
          date: formattedDate,
          type,
          deptCode: zoneCode, // 使用从全局状态获取的 zoneCode 作为 deptCode
        },
      });

      if (response) {
        if (type === 1) {
          const chartData = response.map((item, index) => {
            const mappedItem = {
              ...item,
              weight: item.monthOpWeight || 0,
              laborEff: item.monthLaborEff || 0,
              weightRatio:
                item.monthWeightRatio !== null ? item.monthWeightRatio : null,
              opAttendanceNumber: item.secAttendanceNumber || 0,
              mobileAttendanceNumber: item.secAttendanceNumber || 0,
              opAttendanceRate: item.opAttendanceMtdRate || 0,
            };

            if (index === response.length - 1) {
              console.log(
                '🔍 最后一条数据映射:',
                JSON.stringify(
                  {
                    original: item,
                    mapped: mappedItem,
                    monthWeightRatio: item.monthWeightRatio,
                    mappedWeightRatio: mappedItem.weightRatio,
                  },
                  null,
                  2,
                ),
              );
            }

            return mappedItem;
          });
          updateChartConfigs(chartData, granularity);
        } else {
          updateChartConfigs(response, granularity);
        }
      }
    } catch (error) {
      console.error('获取人员效能趋势图表数据失败:', error);
    }
  };

  // 使用 useRef 来强制刷新 useConditionEffect
  const forceRefreshRef = React.useRef(0);

  useConditionEffect(
    [() => fetchPersonnelEfficiencyData(timeGranularity, selectedDate)],
    [],
    0, // TAB_INDICES.PERSONNEL_EFFICIENCY
    `${timeGranularity}-${dayjs(selectedDate).format(
      timeGranularity === GranularityType.DAY ? 'YYYY-MM-DD' : 'YYYY-MM',
    )}-${zoneCode}-${forceRefreshRef.current}`,
  );

  // 监听 zoneCode 变化，强制刷新数据
  useEffect(() => {
    if (zoneCode) {
      // 增加强制刷新计数器，触发 useConditionEffect 重新执行
      forceRefreshRef.current += 1;
      console.log('🔄 场站切换检测到，强制刷新数据:', {
        zoneCode,
        refreshCount: forceRefreshRef.current,
      });
    }
  }, [zoneCode]);

  const handleDateChange = date => {
    // 检查选择的日期是否为今天或之后，如果是则不允许选择
    const today = dayjs().startOf('day');
    const selectedDay = dayjs(date).startOf('day');

    if (selectedDay.isSame(today) || selectedDay.isAfter(today)) {
      // 如果选择的是今天或之后的日期，显示提示并不更新状态
      console.warn('不能选择今天及今天之后的日期');
      return;
    }

    setSelectedDate(date);
  };

  const handleTimeGranularityChange = newGranularity => {
    if (newGranularity !== timeGranularity) {
      setTimeGranularity(newGranularity);
      // 时间粒度变化后，useConditionEffect会自动监听并重新请求数据
    }
  };

  // 查看更多数据处理
  const handleViewMore = () => {
    // 根据时间粒度设置类型参数：0:日维度,1:月维度
    const type = timeGranularity === GranularityType.DAY ? 0 : 1;
    // 格式化当前选择的日期
    const formattedDate = dayjs(selectedDate).format(
      timeGranularity === GranularityType.DAY ? 'YYYY-MM-DD' : 'YYYY-MM',
    );

    // 构建查询参数
    const queryParams = new URLSearchParams({
      type: type.toString(),
      date: formattedDate,
      timeGranularity: timeGranularity,
      zoneCode: zoneCode || '',
    });

    // 跳转到详情页面，携带日/月类型和相关参数
    history.push(`/personnel-efficiency-detail?${queryParams.toString()}`);
  };

  // 更新图表配置的函数
  const updateChartConfigs = (trendData, granularity) => {
    if (!trendData) return;
    // 截取最后8条数据，以保证chart只展示8条数据（最新数据在最后）
    trendData = Array.isArray(trendData) ? trendData.slice(-8) : trendData;
    // 处理日期格式，根据粒度不同显示不同格式
    const formatDate = dateStr => {
      if (!dateStr) return '';
      const date = dayjs(dateStr);
      return granularity === GranularityType.DAY
        ? date.format('MM-DD')
        : date.format('YYYY-MM');
    };

    // 提取日期数组
    const dateLabels = Array.isArray(trendData)
      ? trendData.map(item => formatDate(item.date))
      : [];

    // 提取货量和劳效数据
    const weightData = Array.isArray(trendData)
      ? trendData.map(item => Number(item.weight.toFixed(2)) || 0)
      : [];
    const laborEffData = Array.isArray(trendData)
      ? trendData.map(item => Number(item.laborEff.toFixed(2)) || 0)
      : [];

    // 提取出勤人数和出勤率数据
    const opAttendanceData = Array.isArray(trendData)
      ? trendData.map(item => item.opAttendanceNumber || 0)
      : [];
    const mobileAttendanceData = Array.isArray(trendData)
      ? trendData.map(item => item.mobileAttendanceNumber || 0)
      : [];
    const attendanceRateData = Array.isArray(trendData)
      ? trendData.map(item => (item.secAttendanceRate || 0) * 100)
      : [];

    // 提取三种出勤率数据（用于月模式的线图）
    const secAttendanceRateData = Array.isArray(trendData)
      ? trendData.map(item => (item.secAttendanceMtdRate || 0) * 100)
      : [];
    const opAttendanceRateData = Array.isArray(trendData)
      ? trendData.map(item => (item.opAttendanceMtdRate || 0) * 100)
      : [];
    const mobileAttendanceRateData = Array.isArray(trendData)
      ? trendData.map(item => (item.mobileAttendanceMtdRate || 0) * 100)
      : [];

    // 更新第一个图表配置 - 货量和劳效
    // 根据时间粒度处理不同的趋势数据
    const processedWeightData = weightData.map((val, index) => {
      const currentItem = trendData && trendData[index];

      const monthCompareValue =
        granularity === GranularityType.MONTH &&
        currentItem &&
        currentItem.weightRatio !== null &&
        typeof currentItem.weightRatio !== 'undefined'
          ? String(currentItem.weightRatio)
          : 'N/A';

      return {
        value: val,
        trend: {
          // 月模式只需要月环比，日模式需要日环比和周同比
          monthCompare: monthCompareValue,
          dayCompare:
            granularity === GranularityType.DAY &&
            currentItem &&
            typeof currentItem.weightRatio !== 'undefined'
              ? String(currentItem.weightRatio)
              : 'N/A',
          weekCompare:
            granularity === GranularityType.DAY &&
            currentItem &&
            typeof currentItem.weekWeightRatio !== 'undefined'
              ? String(currentItem.weekWeightRatio)
              : 'N/A',
        },
        granularity, // 添加粒度信息，供tooltip使用
      };
    });

    const processedLaborEffData = laborEffData.map((val, index) => ({
      value: val,
      trend: {
        // 月模式只需要月环比，日模式需要日环比和周同比
        monthCompare:
          granularity === GranularityType.MONTH &&
          trendData &&
          trendData[index] &&
          typeof trendData[index].weightRatio !== 'undefined'
            ? String(trendData[index].weightRatio)
            : 'N/A',
        dayCompare:
          granularity === GranularityType.DAY &&
          trendData &&
          trendData[index] &&
          typeof trendData[index].weightRatio !== 'undefined'
            ? String(trendData[index].weightRatio)
            : 'N/A',
        weekCompare:
          granularity === GranularityType.DAY &&
          trendData &&
          trendData[index] &&
          typeof trendData[index].weekWeightRatio !== 'undefined'
            ? String(trendData[index].weekWeightRatio)
            : 'N/A',
      },
      granularity, // 添加粒度信息，供tooltip使用
    }));

    // 计算在职劳效的最大值，用于动态设置Y轴范围
    const maxLaborEff = laborEffData.length > 0 ? Math.max(...laborEffData) : 0;
    const laborEffYAxisMax = Math.ceil(maxLaborEff) + 1; // 最高值+1
    const laborEffInterval = Math.max(1, Math.ceil(laborEffYAxisMax / 4)); // 动态计算间隔

    setChart1Config({
      ...chart1Config,
      xAxis: [
        {
          ...chart1Config.xAxis[0],
          data: dateLabels as any,
        },
      ],
      yAxis: [
        // 保持第一个Y轴（货量）不变
        chart1Config.yAxis[0],
        // 更新第二个Y轴（在职劳效）的最大值和间隔
        {
          ...chart1Config.yAxis[1],
          max: laborEffYAxisMax, // 动态设置最大值
          interval: laborEffInterval, // 动态设置间隔
        },
      ],
      series: [
        {
          name: '货量(吨)',
          type: 'bar',
          barWidth: 8,
          itemStyle: {
            color: '#1890ff',
          },
          data: processedWeightData as any, // 使用处理后的数据
        },
        {
          name: '在职劳效',
          type: 'line',
          yAxisIndex: 1,
          itemStyle: {
            color: '#ff4d4f',
          },
          lineStyle: {
            color: '#ff4d4f',
          },
          data: processedLaborEffData as any, // 使用处理后的数据
        },
      ],
    } as any);

    // 处理第二个图表的数据，添加趋势信息
    const processedOpAttendanceData = opAttendanceData.map((val, index) => ({
      value: val,
      trend: {
        secAttendanceRatio:
          trendData &&
          trendData[index] &&
          typeof trendData[index].secAttendanceRatio !== 'undefined'
            ? String(trendData[index].secAttendanceRatio)
            : 'N/A',
        secAttendanceRate:
          trendData &&
          trendData[index] &&
          typeof trendData[index].secAttendanceRate !== 'undefined'
            ? String(trendData[index].secAttendanceRate)
            : 'N/A',
      },
    }));

    const processedMobileAttendanceData = mobileAttendanceData.map(
      (val, index) => ({
        value: val,
        trend: {
          secAttendanceRatio:
            trendData &&
            trendData[index] &&
            typeof trendData[index].secAttendanceRatio !== 'undefined'
              ? String(trendData[index].secAttendanceRatio)
              : 'N/A',
        },
      }),
    );

    const processedAttendanceRateData = attendanceRateData.map(
      (val, index) => ({
        value: val,
        trend: {
          secAttendanceRatio:
            trendData &&
            trendData[index] &&
            typeof trendData[index].secAttendanceRatio !== 'undefined'
              ? String(trendData[index].secAttendanceRatio)
              : 'N/A',
        },
      }),
    );

    // 更新第二个图表配置 - 根据时间粒度显示不同内容
    if (granularity === GranularityType.MONTH) {
      // 月模式：显示三条出勤率线图
      setChart2Config({
        ...chart2Config,
        xAxis: [
          {
            ...chart2Config.xAxis[0],
            data: dateLabels,
          },
        ],
        yAxis: [
          {
            type: 'value',
            min: 0,
            max: 100,
            interval: 25,
            // 隐藏坐标轴线
            axisLine: {
              show: false,
            },
            // 隐藏刻度线
            axisTick: {
              show: false,
            },
            // 保留轴标签
            axisLabel: {
              formatter: '{value}%',
            },
            // 显示网格线
            splitLine: {
              show: true,
              lineStyle: {
                color: '#f0f0f0',
                type: 'dashed',
              },
            },
            position: 'left',
          },
        ],
        legend: {
          left: 'left',
          top: 'top',
          orient: 'horizontal',
          itemHeight: 8,
          itemWidth: 8,
          data: [
            {
              name: '自有二线出勤率',
              textStyle: {
                color: 'black',
                fontSize: 12,
              },
            },
            {
              name: '操作员出勤率',
              textStyle: {
                color: 'black',
                fontSize: 12,
              },
            },
            {
              name: '移动员出勤率',
              textStyle: {
                color: 'black',
                fontSize: 12,
              },
            },
          ],
        },
        series: [
          {
            name: '自有二线出勤率',
            type: 'line',
            itemStyle: {
              color: '#1890ff',
            },
            lineStyle: {
              color: '#1890ff',
            },
            symbol: 'circle',
            symbolSize: 6,
            tooltip: {
              valueFormatter: (value: any) => `${value}%`,
            },
            data: secAttendanceRateData,
          },
          {
            name: '操作员出勤率',
            type: 'line',
            itemStyle: {
              color: '#ff7f00',
            },
            lineStyle: {
              color: '#ff7f00',
            },
            symbol: 'circle',
            symbolSize: 6,
            tooltip: {
              valueFormatter: (value: any) => `${value}%`,
            },
            data: opAttendanceRateData,
          },
          {
            name: '移动员出勤率',
            type: 'line',
            itemStyle: {
              color: '#00d4aa',
            },
            lineStyle: {
              color: '#00d4aa',
            },
            symbol: 'circle',
            symbolSize: 6,
            tooltip: {
              valueFormatter: (value: any) => `${value}%`,
            },
            data: mobileAttendanceRateData,
          },
        ],
      });
    } else {
      // 日模式：保持原有的出勤人数和出勤率显示
      setChart2Config({
        ...chart2Config,
        xAxis: [
          {
            ...chart2Config.xAxis[0],
            data: dateLabels,
          },
        ],
        yAxis: [
          {
            type: 'value',
            nameLocation: 'end',
            min: 0,
            // 动态设置最大值和间隔，避免标签重叠
            splitNumber: 4, // 限制分割段数，避免标签过密
            minInterval: 1, // 设置最小间隔，避免小数
            // 隐藏坐标轴线
            axisLine: {
              show: false,
            },
            // 隐藏刻度线
            axisTick: {
              show: false,
            },
            // 保留轴标签
            axisLabel: {
              formatter: '{value}',
            },
            position: 'left',
          },
          {
            type: 'value',
            min: 0,
            max: 100,
            interval: 25,
            // 隐藏坐标轴线
            axisLine: {
              show: false,
            },
            // 隐藏刻度线
            axisTick: {
              show: false,
            },
            // 保留轴标签
            axisLabel: {
              formatter: '{value}%',
            },
            // 隐藏网格线
            splitLine: {
              show: false,
            },
            position: 'right',
          },
        ],
        legend: {
          left: 'left',
          top: 'top',
          orient: 'horizontal',
          itemHeight: 8,
          itemWidth: 8,
          data: [
            {
              name: '操作出勤人数',
              textStyle: {
                color: 'black',
                fontSize: 12,
              },
            },
            {
              name: '移动出勤人数',
              textStyle: {
                color: 'black',
                fontSize: 12,
              },
            },
            {
              name: '出勤率',
              textStyle: {
                color: 'black',
                fontSize: 12,
              },
            },
          ],
        },
        series: [
          {
            name: '操作出勤人数',
            type: 'bar',
            stack: 'attendance',
            barWidth: 8,
            itemStyle: {
              color: '#1890ff',
            },
            data: processedOpAttendanceData, // 使用处理后的数据
          },
          {
            name: '移动出勤人数',
            type: 'bar',
            stack: 'attendance',
            barWidth: 8,
            itemStyle: {
              color: '#13c2c2',
            },
            data: processedMobileAttendanceData, // 使用处理后的数据
          },
          {
            name: '出勤率',
            type: 'line',
            yAxisIndex: 1,
            itemStyle: {
              color: '#52c41a',
            },
            lineStyle: {
              color: '#52c41a',
            },
            tooltip: {
              valueFormatter: (value: any) => `${value}%`,
            },
            data: processedAttendanceRateData, // 使用处理后的数据
          },
        ],
      });
    }
  };

  const [chart1Config, setChart1Config] = useState({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      formatter(params) {
        // 获取当前日期和数据
        const currentDate = params[0].axisValueLabel;
        const cargoVolumeParam = params.find(p => p.seriesName === '货量(吨)');
        const efficiencyParam = params.find(p => p.seriesName === '在职劳效');
        // 由于系列数据项现在是 {value: ..., trend: ...} 结构，需要从 .data.value 或 .value 获取值
        const cargoVolume =
          cargoVolumeParam?.data?.value ?? cargoVolumeParam?.value ?? 0;
        const efficiency =
          efficiencyParam?.data?.value ?? efficiencyParam?.value ?? 0;

        // 从第一个系列的数据点中检索趋势数据和粒度信息
        const pointData = params[0]?.data;
        const compareData = pointData?.trend || {};
        const currentGranularity = pointData?.granularity;

        // 处理环比数据 - 直接使用服务端返回的字段乘以100，保留两位小数
        const formatRatio = (ratio: any) => {
          if (ratio === 'N/A' || ratio === undefined || ratio === null)
            return 'N/A';

          // 将字符串转换为数字（如果已经是数字则不变）
          const numRatio = Number(ratio);

          // 检查是否为有效数字
          if (Number.isNaN(numRatio)) return String(ratio);

          // 直接使用服务端返回的字段乘以100，保留两位小数
          const percentageValue = (numRatio * 100).toFixed(2);
          return numRatio > 0
            ? `+${percentageValue}%`
            : numRatio < 0
            ? `${percentageValue}%`
            : '0%';
        };

        // 根据时间粒度显示不同的内容
        if (currentGranularity === GranularityType.MONTH) {
          // 月模式：只显示货量、在职劳效、货量月环比
          console.log(
            '🔍 月模式Tooltip调试:',
            JSON.stringify(
              {
                compareData,
                monthCompare: compareData.monthCompare,
                pointData,
                currentGranularity,
              },
              null,
              2,
            ),
          );

          const monthCompareStr = formatRatio(compareData.monthCompare);
          const monthCompareValue = parseFloat(compareData.monthCompare);
          const monthCompareColor =
            monthCompareValue > 0
              ? '#52c41a'
              : monthCompareValue < 0
              ? '#ff4d4f'
              : '#999';
          const monthCompareArrow =
            monthCompareValue > 0 ? '↑' : monthCompareValue < 0 ? '↓' : '';

          return `
            <div style="padding: 10px; border-radius: 4px; color: #fff;">
              <div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">${currentDate}</div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 80px;">货量</span>
                <span style="font-weight: bold;">${cargoVolume}T</span>
              </div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 80px;">在职劳效</span>
                <span style="font-weight: bold;">${efficiency}</span>
              </div>
              <div>
                <span style="display:inline-block; width: 80px;">月环比</span>
                <span style="color: ${monthCompareColor};">${monthCompareStr} ${monthCompareArrow}</span>
              </div>
            </div>
          `;
        } else {
          // 日模式：显示货量、在职劳效、日环比、周同比
          const dayCompareStr = formatRatio(compareData.dayCompare);
          const weekCompareStr = formatRatio(compareData.weekCompare);

          const dayCompareValue = parseFloat(compareData.dayCompare);
          const weekCompareValue = parseFloat(compareData.weekCompare);

          const dayCompareColor =
            dayCompareValue > 0
              ? '#52c41a'
              : dayCompareValue < 0
              ? '#ff4d4f'
              : '#999';
          const weekCompareColor =
            weekCompareValue > 0
              ? '#52c41a'
              : weekCompareValue < 0
              ? '#ff4d4f'
              : '#999';
          const dayCompareArrow =
            dayCompareValue > 0 ? '↑' : dayCompareValue < 0 ? '↓' : '';
          const weekCompareArrow =
            weekCompareValue > 0 ? '↑' : weekCompareValue < 0 ? '↓' : '';

          return `
            <div style="padding: 10px; border-radius: 4px; color: #fff;">
              <div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">${currentDate}</div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 70px;">货量</span>
                <span style="font-weight: bold;">${cargoVolume}T</span>
              </div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 70px;">在职劳效</span>
                <span style="font-weight: bold;">${efficiency}</span>
              </div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 70px;">日环比</span>
                <span style="color: ${dayCompareColor};">${dayCompareStr} ${dayCompareArrow}</span>
              </div>
            </div>
          `;
        }
      },
    },
    legend: {
      // 将图例放在左上角
      left: 'left',
      top: '0',
      orient: 'horizontal',
      itemHeight: 8,
      itemWidth: 8,
      data: [
        {
          name: '货量(吨)',
          textStyle: {
            color: 'black',
            fontSize: 12,
          },
        },
        {
          name: '在职劳效',
          textStyle: {
            color: 'black',
            fontSize: 12,
          },
        },
      ],
    },
    grid: {
      top: '25%',
      left: '3%', // 增加左边距，为Y轴标签留出更多空间
      right: '4%', // 增加右边距，为右侧Y轴标签留出空间
      bottom: '3%', // 增加底部边距
      // left: '8%', // 增加左边距，为Y轴标签留出更多空间
      // right: '8%', // 增加右边距，为右侧Y轴标签留出空间
      // bottom: '8%', // 增加底部边距
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: [
          // '12-27',
          // '12-28',
          // '12-29',
          // '12-30',
          // '12-31',
          // '01-01',
          // '01-02',
          // '01-03',
        ],
        axisPointer: {
          type: 'shadow',
        },
        // 隐藏坐标轴线
        axisLine: {
          show: false,
        },
        // 隐藏刻度线
        axisTick: {
          show: false,
        },
        // 保留轴标签
        axisLabel: {
          show: true,
        },
        // 隐藏网格线
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        nameLocation: 'end',
        min: 0,
        // 动态设置最大值和间隔，避免标签重叠
        splitNumber: 4, // 限制分割段数，避免标签过密
        minInterval: 1, // 设置最小间隔，避免小数
        axisLabel: {
          formatter: '{value}',
          // 增加标签间距，避免重叠
          margin: 8,
          fontSize: 10,
          // 设置标签旋转，避免重叠
          rotate: 0,
          // 限制标签显示间隔
          interval: 0,
        },
        position: 'left',
        // 隐藏坐标轴线
        axisLine: {
          show: false,
        },
        // 隐藏刻度线
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      {
        type: 'value',
        min: 0,
        max: 100,
        interval: 25,
        axisLabel: {
          formatter: '{value}',
        },
        // splitLine: {
        //   show: false,
        // },
        position: 'right',
        // 隐藏坐标轴线
        axisLine: {
          show: false,
          // show: true,
        },
        // 隐藏刻度线
        axisTick: {
          show: false,
          // show: true,
        },
      },
    ],
    series: [
      {
        name: '货量(吨)',
        type: 'bar',
        // 设置柱状图为蓝色
        itemStyle: {
          color: '#3A7BD5',
        },
        // 降低柱状图宽度
        barWidth: 8, // 降低柱状图宽度为默认的30%
        tooltip: {
          valueFormatter: value => value + ' 吨',
        },
        data: [],
        // data: [1200, 1600, 1100, 1800, 0, 1500, 1500, 0],
      },
      {
        name: '在职劳效',
        type: 'line',
        yAxisIndex: 1,
        // 设置线为黄色
        itemStyle: {
          color: '#F5A623',
        },
        lineStyle: {
          color: '#F5A623',
        },
        barWidth: 8, // 降低柱状图宽度为默认的30%
        tooltip: {
          valueFormatter: value => value,
        },
        data: [],
        // data: [50, 60, 55, 65, 0, 75, 80, 0],
      },
    ],
  });

  const [chart2Config, setChart2Config] = useState({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      formatter(params) {
        // 获取当前日期和数据
        const currentDate = params[0].axisValueLabel;

        // 检查是否为月模式的三线图
        const isThreeLineMode = params.some(
          p =>
            p.seriesName === '自有二线出勤率' ||
            p.seriesName === '操作员出勤率' ||
            p.seriesName === '移动员出勤率',
        );

        if (isThreeLineMode) {
          // 月模式：显示三种出勤率
          const secAttendanceParam = params.find(
            p => p.seriesName === '自有二线出勤率',
          );
          const opAttendanceParam = params.find(
            p => p.seriesName === '操作员出勤率',
          );
          const mobileAttendanceParam = params.find(
            p => p.seriesName === '移动员出勤率',
          );

          const secAttendanceRate = (secAttendanceParam?.value ?? 0).toFixed(1);
          const opAttendanceRate = (opAttendanceParam?.value ?? 0).toFixed(1);
          const mobileAttendanceRate = (
            mobileAttendanceParam?.value ?? 0
          ).toFixed(1);

          return `
            <div style="padding: 10px; border-radius: 4px; color: #fff;">
              <div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">${currentDate}</div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 100px;">自有二线出勤率</span>
                <span style="font-weight: bold;">${secAttendanceRate}%</span>
              </div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 100px;">操作员出勤率</span>
                <span style="font-weight: bold;">${opAttendanceRate}%</span>
              </div>
              <div>
                <span style="display:inline-block; width: 100px;">移动员出勤率</span>
                <span style="font-weight: bold;">${mobileAttendanceRate}%</span>
              </div>
            </div>
          `;
        } else {
          // 日模式：显示出勤人数和出勤率
          const opAttendanceParam = params.find(
            p => p.seriesName === '操作出勤人数',
          );
          const mobileAttendanceParam = params.find(
            p => p.seriesName === '移动出勤人数',
          );
          const secAttendanceRateParam = params.find(
            p => p.seriesName === '出勤率',
          );

          // 由于系列数据项可能是 {value: ..., trend: ...} 结构，需要从 .data.value 或 .value 获取值
          const opAttendanceNumber =
            opAttendanceParam?.data?.value ?? opAttendanceParam?.value ?? 0;
          const mobileAttendanceNumber =
            mobileAttendanceParam?.data?.value ??
            mobileAttendanceParam?.value ??
            0;
          const secAttendanceRate = (
            secAttendanceRateParam?.data?.value ??
            secAttendanceRateParam?.value ??
            0
          ).toFixed(2);

          // 从第一个系列的数据点中检索趋势数据
          const pointData = params[0]?.data;
          const compareData = pointData?.trend || { secAttendanceRatio: 'N/A' };

          // 处理环比数据 - 直接使用服务端返回的字段乘以100，保留两位小数
          const formatRatio = ratio => {
            if (ratio === 'N/A' || ratio === undefined) return 'N/A';
            const numRatio = Number(ratio);
            if (Number.isNaN(numRatio)) return String(ratio);
            // 直接使用服务端返回的字段乘以100，保留两位小数
            const percentageValue = (numRatio * 100).toFixed(2);
            return numRatio > 0
              ? `+${percentageValue}%`
              : numRatio < 0
              ? `${percentageValue}%`
              : '0%';
          };

          const secAttendanceRatioStr = formatRatio(
            compareData.secAttendanceRatio,
          );
          const secAttendanceRatioValue = parseFloat(
            compareData.secAttendanceRatio,
          );
          const secAttendanceRatioColor =
            secAttendanceRatioValue > 0
              ? '#52c41a'
              : secAttendanceRatioValue < 0
              ? '#ff4d4f'
              : '#999';
          const secAttendanceRatioArrow =
            secAttendanceRatioValue > 0
              ? '↑'
              : secAttendanceRatioValue < 0
              ? '↓'
              : '';

          return `
            <div style="padding: 10px; border-radius: 4px; color: #fff;">
              <div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">${currentDate}</div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 100px;">操作出勤人数</span>
                <span style="font-weight: bold;">${opAttendanceNumber}人</span>
              </div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 100px;">移动出勤人数</span>
                <span style="font-weight: bold;">${mobileAttendanceNumber}人</span>
              </div>
              <div style="margin-bottom: 6px;">
                <span style="display:inline-block; width: 100px;">出勤率</span>
                <span style="font-weight: bold;">${secAttendanceRate}%</span>
              </div>
              <div>
                <span style="display:inline-block; width: 100px;">日出勤率环比</span>
                <span style="color: ${secAttendanceRatioColor};">${secAttendanceRatioStr} ${secAttendanceRatioArrow}</span>
              </div>
            </div>
          `;
        }
      },
    },
    legend: {
      // 将图例放在左上角
      left: 'left',
      top: 'top',
      orient: 'horizontal',
      itemHeight: 8,
      itemWidth: 8,
      data: [
        {
          name: '操作出勤人数',
          textStyle: {
            color: 'black',
            fontSize: 12,
          },
        },
        {
          name: '移动出勤人数',
          textStyle: {
            color: 'black',
            fontSize: 12,
          },
        },
        {
          name: '出勤率',
          textStyle: {
            color: 'black',
            fontSize: 12,
          },
        },
      ],
    },
    grid: {
      top: '25%',
      left: '3%', // 增加左边距，为Y轴标签留出更多空间
      right: '4%', // 增加右边距，为右侧Y轴标签留出空间
      bottom: '3%', // 增加底部边距
      // left: '8%', // 增加左边距，为Y轴标签留出更多空间
      // right: '8%', // 增加右边距，为右侧Y轴标签留出空间
      // bottom: '8%', // 增加底部边距
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: [],
        axisPointer: {
          type: 'shadow',
        },
        // 隐藏坐标轴线
        axisLine: {
          show: false,
        },
        // 隐藏刻度线
        axisTick: {
          show: false,
        },
        // 保留轴标签
        axisLabel: {
          show: true,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        nameLocation: 'end',
        min: 0,
        // 动态设置最大值和间隔，避免标签重叠
        splitNumber: 4, // 限制分割段数，避免标签过密
        minInterval: 1, // 设置最小间隔，避免小数
        // 隐藏坐标轴线
        axisLine: {
          show: false,
        },
        // 隐藏刻度线
        axisTick: {
          show: false,
        },
        // 保留轴标签
        axisLabel: {
          formatter: '{value}',
          // 增加标签间距，避免重叠
          margin: 8,
          fontSize: 10,
          // 设置标签旋转，避免重叠
          rotate: 0,
          // 限制标签显示间隔
          interval: 0,
        },
        position: 'left',
      },
      {
        type: 'value',
        min: 0,
        max: 100,
        interval: 25,
        // 隐藏坐标轴线
        axisLine: {
          show: false,
        },
        // 隐藏刻度线
        axisTick: {
          show: false,
        },
        // 保留轴标签
        axisLabel: {
          formatter: '{value}%',
        },
        // 隐藏网格线
        splitLine: {
          show: false,
        },
        position: 'right',
      },
    ],
    series: [
      {
        name: '操作出勤人数',
        type: 'bar',
        stack: 'attendance',
        // 设置柱状图为蓝色
        itemStyle: {
          color: '#3A7BD5',
        },
        // 降低柱状图宽度
        barWidth: 3,
        tooltip: {
          valueFormatter: value => `${value} 人`,
        },
        data: [],
        // data: [36, 45, 35, 10, 20, 25, 36, 0],
      },
      {
        name: '移动出勤人数',
        type: 'bar',
        stack: 'attendance',
        // 设置柱状图为橙色
        itemStyle: {
          color: '#F5A623',
        },
        // 降低柱状图宽度
        barWidth: 3,
        tooltip: {
          valueFormatter: value => `${value} 人`,
        },
        data: [],
        // data: [25, 40, 30, 10, 15, 20, 25, 0],
      },
      {
        name: '出勤率',
        type: 'line',
        yAxisIndex: 1,
        // 设置线为绿色
        itemStyle: {
          color: '#52c41a',
        },
        lineStyle: {
          color: '#52c41a',
        },
        tooltip: {
          valueFormatter: value => `${value}%`,
        },
        data: [],
        // data: [40, 50, 55, 60, 65, 70, 75, 0],
      },
    ],
  });

  return (
    <section className="PersonnelEfficiency-root">
      {/* 人效趋势模块标题和日期选择器 */}
      <PersonnelEfficiencyHeader
        timeGranularity={timeGranularity}
        selectedDate={selectedDate}
        onTimeGranularityChange={handleTimeGranularityChange}
        onDateChange={handleDateChange}
      />
      {/* 效率数据卡片 */}
      <EfficiencyCards
        data={efficiencyCards}
        timeGranularity={timeGranularity}
        loading={loading}
      />

      {/* 人效趋势图表 */}
      <div className="chart-container">
        <EchartComponent config={chart1Config} className="chart" />
      </div>
      {/* 人效趋势图表 */}
      <div className="chart-container">
        <EchartComponent config={chart2Config} className="chart" />
      </div>
      <div className="view-more-container" onClick={handleViewMore}>
        查看更多数据
        <img src={arrowImg} className="view-more-container-arrow" />
      </div>

      {/* 各组别累计人数比例组件 */}
      <GroupPersonnelRatio deptCode={zoneCode} />

      {/* 劳效异常组件 */}
      <LaborEfficiencyAbnormal zoneCode={zoneCode} />
    </section>
  );
};

function mapStateToProps(state) {
  return {
    batchCode: state.global?.batchCode || '',
    newBatchCode: state.global?.newBatchCode || '',
    zoneCode: state.global?.zoneCode || '', // 这将作为 deptCode 传递给接口
    planBeginTm: state.global?.planBeginTm || '',
  };
}

export const PersonnelEfficiency = connect(mapStateToProps)(
  PersonnelEfficiencyComp,
);
