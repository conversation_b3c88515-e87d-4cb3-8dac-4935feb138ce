import React from 'react';
import './index.scss';
import arrowUpIcon from '../../images/<EMAIL>';

export const EfficiencyCards = ({
  data = {},
  timeGranularity = 'date',
  loading = false,
}) => {
  // 根据时间粒度和接口数据结构重新组织卡片数据
  // 数据版本检查：确保数据和当前时间粒度匹配，避免显示错误的环比类型
  const isDataMatching = data.dataType === timeGranularity;

  // 当数据不匹配时，使用默认值避免显示错误数据
  const safeData = isDataMatching ? data : {};

  // 环比数据处理：直接使用服务端返回的字段乘以100，保留两位小数
  const weightRatio =
    isDataMatching &&
    data.weightRatio !== undefined &&
    data.weightRatio !== null
      ? (data.weightRatio * 100).toFixed(2)
      : 0;

  const cardsData =
    timeGranularity === 'month'
      ? [
          // 月类型：只显示4个字段，按2x2布局
          {
            title: '月操作货量',
            num: safeData.opWeight || 0,
            unit: 'T',
            descLabel: '月环比',
            descValue: `${Number(weightRatio) > 0 ? '+' : ''}${weightRatio}%`,
            isUp: Number(weightRatio) > 0,
            type: 'primary',
          },
          {
            title: '操作劳效',
            num: safeData.opAttendanceEff || 0,
            unit: '吨/人',
            desc: '',
            type: 'efficiency',
          },
          {
            title: '移动劳效',
            num: safeData.mobileAttendanceEff || 0,
            unit: '吨/人',
            desc: '',
            type: 'efficiency',
          },
          {
            title: '在职劳效',
            num: safeData.laborEff || 0,
            unit: '吨/人',
            desc: '',
            type: 'efficiency',
          },
        ]
      : [
          // 日类型：显示原有的6个字段
          // 第一行
          {
            title: '日操作货量',
            num: safeData.opWeight || 0,
            unit: 'T',
            descLabel: '日环比',
            descValue: `${Number(weightRatio) > 0 ? '+' : ''}${weightRatio}%`,
            isUp: Number(weightRatio) > 0,
            type: 'primary',
          },
          {
            title: '操作在职人数',
            num: safeData.opAttendanceNumber || 0,
            unit: '人',
            desc: '',
            type: 'secondary',
          },
          {
            title: '移动在职人数',
            num: safeData.mobileAttendanceNumber || 0,
            unit: '人',
            desc: '',
            type: 'secondary',
          },
          // 第二行
          {
            title: '操作劳效',
            num: safeData.opAttendanceEff || 0,
            unit: '吨/人',
            desc: '',
            type: 'efficiency',
          },
          {
            title: '移动劳效',
            num: safeData.mobileAttendanceEff || 0,
            unit: '吨/人',
            desc: '',
            type: 'efficiency',
          },
          {
            title: '在职劳效',
            num: safeData.laborEff || 0,
            unit: '吨/人',
            desc: '',
            type: 'efficiency',
          },
        ];

  return (
    <section className="EfficiencyCards-root">
      <div
        className={`cards-container ${
          timeGranularity === 'month' ? 'month-layout' : 'day-layout'
        }`}
      >
        {cardsData.map(item => (
          <div key={item.title} className={`efficiency-card ${item.type}`}>
            <div className="card-title">{item.title}</div>
            <div className="card-content">
              <span className="card-number">
                {typeof item.num === 'number'
                  ? item.num.toFixed(
                      item.type === 'primary' && item.unit === 'T' ? 0 : 1,
                    )
                  : item.num}
              </span>
              {item.unit && <span className="card-unit">{item.unit}</span>}
            </div>
            {/* 新格式：分离的标签和数值 */}
            {(item.descLabel || item.descValue) && (
              <div className="card-desc-container">
                {item.descLabel && (
                  <div className="desc-label">{item.descLabel}</div>
                )}
                {item.descValue && (
                  <div className={`desc-value ${item.isUp ? 'up' : 'down'}`}>
                    {item.descValue}
                    {item.isUp && (
                      <img
                        src={arrowUpIcon}
                        alt="上升箭头"
                        className="desc-arrow-img"
                      />
                    )}
                  </div>
                )}
              </div>
            )}
            {/* 旧格式：兼容性支持 */}
            {item.desc && !item.descLabel && !item.descValue && (
              <div className={`card-desc ${item.isUp ? 'up' : 'down'}`}>
                {item.desc}
                {item.isUp && (
                  <img
                    src={arrowUpIcon}
                    alt="上升箭头"
                    className="desc-arrow-img"
                  />
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </section>
  );
};
