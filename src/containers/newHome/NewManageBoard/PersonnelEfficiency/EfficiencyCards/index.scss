.EfficiencyCards-root {
  margin: 12px 0px;
  background: #f0f3f9;
  width: 324px;
  border-radius: 4px;
  padding: 12px 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .cards-container {
    display: grid;
    gap: 12px;
    align-items: start;

    // 日类型布局：3列2行
    &.day-layout {
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, auto);
    }

    // 月类型布局：2行2列
    &.month-layout {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, auto);
    }

    .efficiency-card {
      // text-align: center;
      // padding: 12px 6px 8px 6px;

      .card-title {
        font-family: PingFang SC;
        font-size: 13px;
        color: #666;
        margin-bottom: 4px;
        line-height: 1.3;
        font-weight: normal;
        word-break: keep-all;
      }

      .card-content {
        font-family: DINPro;
        display: flex;
        align-items: baseline;
        // justify-content: center;
        margin-bottom: 6px;

        .card-number {
          font-size: 20px;
          font-weight: 400;
          color: #333;
          line-height: 1;
          font-weight: 700;
        }

        .card-unit {
          font-size: 12px;
          color: #666;
          margin-left: 2px;
          font-weight: normal;
        }

        .card-arrow {
          font-size: 12px;
          color: #ff6a17;
          margin-left: 4px;
          transform: translateY(-2px);
        }
      }

      .card-desc {
        font-size: 10px;
        color: #ff6a17;
        font-weight: 500;
        display: inline-block;

        background: #ffffff;
        height: 24px;
        line-height: 24px;
        padding: 0 3.5px;
        border-radius: 4px;

        .desc-arrow {
          margin-left: 2px;
          font-size: 10px;
        }

        .desc-arrow-img {
          width: 12px;
          height: 12px;
          margin-left: 4px;
          vertical-align: middle;
        }
      }

      .card-desc-container {
        display: flex;
        align-items: center;
        // flex-direction: column;
        // gap: 4px;

        .desc-label {
          font-size: 8px;
          color: #666;
          font-weight: 400;
        }

        .desc-value {
          font-size: 10px;
          font-weight: 500;
          display: inline-flex;
          padding: 0 3.5px;
          border-radius: 4px;
          display: flex;
          align-items: center;

          .desc-arrow-img {
            width: 5px;
            height: 7px;
            margin-left: 4px;
          }
        }
      }
    }
  }
}

// 响应式优化
// @media (max-width: 375px) {
//   .EfficiencyCards-root {
//     margin: 8px 12px 16px 12px;
//     padding: 16px 12px;

//     .cards-container {
//       gap: 8px;
//     }

//     .efficiency-card {
//       padding: 10px 4px 8px 4px;

//       .card-title {
//         font-size: 10px;
//         margin-bottom: 8px;
//       }

//       .card-content .card-number {
//         font-size: 18px;
//       }
//     }
//   }
// }

// @media (max-width: 320px) {
//   .EfficiencyCards-root {
//     .cards-container {
//       gap: 6px;
//     }

//     .efficiency-card {
//       padding: 8px 2px 6px 2px;

//       .card-title {
//         font-size: 9px;
//       }

//       .card-content .card-number {
//         font-size: 16px;
//       }
//     }
//   }
// }
