import React from 'react';
import dayjs from 'dayjs';
import { DateSelect } from 'src/components/DateSelect';
import './PersonnelEfficiencyHeader.scss';

/**
 * 人效趋势模块标题和日期选择器组件
 * @param {Object} props 组件属性
 * @param {string} props.title 标题文本
 * @param {string} props.timeGranularity 时间粒度（'date'或'month'），由父组件传入
 * @param {Date} props.selectedDate 选中的日期，由父组件传入
 * @param {Function} props.onTimeGranularityChange 时间粒度变更回调，由父组件传入
 * @param {Function} props.onDateChange 日期变更回调，由父组件传入
 */
const PersonnelEfficiencyHeader = ({
  title = '人效趋势 (T-1)',
  timeGranularity,
  selectedDate,
  onTimeGranularityChange,
  onDateChange,
}) => (
  <>
    <div className="header-title">{title}</div>
    <div className="header-section">
      <div className="time-granularity-switcher">
        <button
          type="button"
          className={`granularity-btn ${
            timeGranularity === 'date' ? 'active' : ''
          }`}
          onClick={() => onTimeGranularityChange('date')}
        >
          日
        </button>
        <button
          type="button"
          className={`granularity-btn ${
            timeGranularity === 'month' ? 'active' : ''
          }`}
          onClick={() => onTimeGranularityChange('month')}
        >
          月
        </button>
      </div>
      <div className="date-selector">
        <DateSelect
          mode={timeGranularity}
          value={selectedDate}
          onChange={onDateChange}
          placeholder={timeGranularity === 'date' ? '选择日期' : '选择月份'}
          format={timeGranularity === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM'}
          maxDate={dayjs()
            .subtract(1, 'day')
            .toDate()}
        />
      </div>
    </div>
  </>
);

export default PersonnelEfficiencyHeader;
