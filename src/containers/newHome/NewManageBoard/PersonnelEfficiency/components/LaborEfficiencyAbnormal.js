import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import './LaborEfficiencyAbnormal.scss';
import simpleRequest from 'src/utils/simpleRequest';
import dayjs from 'dayjs';
import { DateSelect } from 'src/components/DateSelect';
import arrow from '../../images/arrow.svg';

/**
 * 劳效异常组件
 * 展示无货量人数和低效人数数据
 */
const LaborEfficiencyAbnormal = ({ zoneCode }) => {
  const history = useHistory();
  const [selectedDate, setSelectedDate] = useState(
    new Date(Date.now() - 86400000),
  );
  const [loading, setLoading] = useState(false);
  const [exceptionData, setExceptionData] = useState({
    threeDaysNoweightNumber: 0,
    eightDownWeightNumber: 0,
  });

  const handleDateChange = date => {
    // 检查选择的日期是否为今天或之后，如果是则不允许选择
    const today = dayjs().startOf('day');
    const selectedDay = dayjs(date).startOf('day');

    if (selectedDay.isSame(today) || selectedDay.isAfter(today)) {
      // 如果选择的是今天或之后的日期，显示提示并不更新状态
      console.warn('不能选择今天及今天之后的日期');
      return;
    }

    setSelectedDate(date);
    fetchLaborEfficiencyExceptionData(date);
  };

  // 获取劳效异常数据
  const fetchLaborEfficiencyExceptionData = async date => {
    setLoading(true);

    try {
      const type = 0; // 固定为日维度
      const formattedDate = dayjs(date).format('YYYY-MM-DD 00:00:00');

      // 调用接口获取劳效异常数据
      const response = await simpleRequest({
        url: '/cockpit/kg/personEff/getPersonEffException',
        method: 'POST',
        data: {
          date: formattedDate,
          type,
          deptCode: zoneCode, // 使用从全局状态获取的 zoneCode 作为 deptCode
        },
      });

      // 处理接口返回的数据
      if (response) {
        setExceptionData({
          threeDaysNoweightNumber: response?.threeDaysNoweightNumber || 0,
          eightDownWeightNumber: response?.eightDownWeightNumber || 0,
        });
      }
    } catch (error) {
      // 错误处理，避免使用 console
      setExceptionData({
        threeDaysNoweightNumber: 0,
        eightDownWeightNumber: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  // 跳转到3天无货量详情页面
  const handleNavigateToThreeDaysNoWeightDetail = () => {
    // 传递当前筛选的日期数据
    const queryParams = new URLSearchParams({
      date: dayjs(selectedDate).format('YYYY-MM-DD'),
      timeGranularity: 'date',
      zoneCode: zoneCode || '',
    });
    history.push(`/three-days-no-weight-detail?${queryParams.toString()}`);
  };

  // 跳转到低于8吨人数详情页面
  const handleNavigateToEightDownWeightDetail = () => {
    // 传递当前筛选的日期数据
    const queryParams = new URLSearchParams({
      date: dayjs(selectedDate).format('YYYY-MM-DD'),
      timeGranularity: 'date',
      zoneCode: zoneCode || '',
    });
    history.push(`/eight-down-weight-detail?${queryParams.toString()}`);
  };

  // 组件挂载或 zoneCode、selectedDate 变化时获取数据
  useEffect(() => {
    if (zoneCode) {
      fetchLaborEfficiencyExceptionData(selectedDate);
    }
  }, [zoneCode, selectedDate]);
  return (
    <div className="labor-efficiency-abnormal">
      <div className="header-section">
        <div className="header-title">劳效异常</div>
        <div className="date-selector">
          <DateSelect
            mode="date"
            value={selectedDate}
            onChange={handleDateChange}
            placeholder="选择日期"
            format="YYYY-MM-DD"
            maxDate={dayjs()
              .subtract(1, 'day')
              .toDate()}
          />
        </div>
      </div>
      <div className="labor-efficiency-abnormal-content">
        <div
          className="labor-efficiency-abnormal-item labor-efficiency-abnormal-item-first"
          onClick={handleNavigateToThreeDaysNoWeightDetail}
        >
          <div className="labor-efficiency-abnormal-title">3天无货量人数</div>
          <div className="labor-efficiency-abnormal-count">
            <span className="labor-efficiency-abnormal-number">
              {loading ? '加载中...' : exceptionData.threeDaysNoweightNumber}
            </span>
            <span className="labor-efficiency-abnormal-unit">人</span>
          </div>
          <img src={arrow} className="labor-efficiency-abnormal-arrow" />
        </div>
        <div
          className="labor-efficiency-abnormal-item labor-efficiency-abnormal-item-second"
          onClick={handleNavigateToEightDownWeightDetail}
        >
          <div className="labor-efficiency-abnormal-title">低于8吨人数</div>
          <div className="labor-efficiency-abnormal-count">
            <span className="labor-efficiency-abnormal-number">
              {loading ? '加载中...' : exceptionData.eightDownWeightNumber}
            </span>
            <span className="labor-efficiency-abnormal-unit">人</span>
          </div>
          <img src={arrow} className="labor-efficiency-abnormal-arrow" />
        </div>
      </div>
    </div>
  );
};

export default LaborEfficiencyAbnormal;
