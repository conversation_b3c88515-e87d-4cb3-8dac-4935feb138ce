import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import PropTypes from 'prop-types';

/**
 * EChart 通用组件
 * @param {Object} props
 * @param {Object} props.config - echarts 配置项
 * @param {string} props.className - 自定义类名
 * @param {Object} props.style - 自定义样式
 * @param {string} props.loading - 是否显示加载状态
 * @param {Function} props.onChartReady - 图表准备好的回调函数
 * @param {Function} props.onEvents - 图表事件回调
 * @returns {JSX.Element}
 */
const EchartComponent = props => {
  const {
    config,
    className = '',
    style = {},
    loading = false,
    onChartReady,
    onEvents,
  } = props;

  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  // 初始化图表
  const initChart = () => {
    const chartDom = chartRef.current;
    if (!chartDom) return;

    // 销毁旧实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // 创建新实例
    chartInstance.current = echarts.init(chartDom);

    // 设置加载状态
    if (loading) {
      chartInstance.current.showLoading();
    } else {
      chartInstance.current.hideLoading();
    }

    // 设置配置项
    if (config) {
      chartInstance.current.setOption(config, true);
    }

    // 绑定事件
    if (onEvents && typeof onEvents === 'object') {
      Object.keys(onEvents).forEach(eventName => {
        if (typeof onEvents[eventName] === 'function') {
          chartInstance.current.on(eventName, onEvents[eventName]);
        }
      });
    }

    // 回调函数
    if (onChartReady && typeof onChartReady === 'function') {
      onChartReady(chartInstance.current);
    }
  };

  // 监听配置变化重新渲染图表
  useEffect(() => {
    initChart();
  }, [config, loading]);

  // 监听窗口大小变化，自适应调整
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      // 组件卸载时销毁实例
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  return (
    <div
      ref={chartRef}
      className={`echart-container ${className}`}
      style={{ width: '100%', height: '100%', ...style }}
    />
  );
};

EchartComponent.propTypes = {
  config: PropTypes.object.isRequired,
  className: PropTypes.string,
  style: PropTypes.object,
  loading: PropTypes.bool,
  onChartReady: PropTypes.func,
  onEvents: PropTypes.object,
};

export default EchartComponent;
