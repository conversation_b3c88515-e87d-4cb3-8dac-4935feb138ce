.labor-efficiency-abnormal {
  border-radius: 8px;

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    margin-bottom: 12px;

    .header-title {
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 600;
    }

    // .date-selector {
    //   padding: 8px 12px;
    //   font-size: 14px;
    //   color: #333;
    //   font-weight: normal;
    //   font-family: PingFang SC;
    //   border: 1px solid rgba(255, 255, 255, 0.2);

    //   .placeholder {
    //     color: #999;
    //   }
    // }
  }

  &-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
    background-color: rgba(240, 243, 249, 1);
    border-radius: 4px;
    padding: 12px;
  }

  &-title {
    font-size: 13px;
    color: #666666;
    margin-bottom: 8px;
    font-family: PingFang SC;
    font-weight: normal;
  }

  &-item-first {
    margin-right: 12px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(230, 235, 245, 1);
    }

    &:active {
      background-color: rgba(220, 227, 240, 1);
      transform: scale(0.98);
    }
  }

  &-item-second {
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(230, 235, 245, 1);
    }

    &:active {
      background-color: rgba(220, 227, 240, 1);
      transform: scale(0.98);
    }
  }

  &-count {
    display: flex;
    align-items: baseline;
  }

  &-number {
    font-size: 20px;
    font-weight: 700;
    color: #333;
  }

  &-unit {
    font-size: 12px;
    color: #333333;
    margin-left: 4px;
  }
  &-arrow {
    position: absolute;
    right: 12px;
    width: 13px;
    height: 13px;
  }
}
