import React from 'react';
import './index.scss';

export const GroupEfficiencyTable = ({ data = [] }) => (
  <section className="GroupEfficiencyTable-root">
    <div className="table-header">
      <div className="header-row">
        <span className="header-cell group-name">装卸组别</span>
        <span className="header-cell">总效率</span>
        <span className="header-cell">当月效率</span>
        <span className="header-cell">日环比</span>
        <span className="header-cell">同期环比</span>
      </div>
    </div>

    <div className="table-body">
      {data.map(item => (
        <div key={item.groupName} className="body-row">
          <span className="body-cell group-name">{item.groupName}</span>
          <span className="body-cell">56.1</span>
          <span className="body-cell">{item.operatingEfficiency}</span>
          <span
            className={`body-cell ${
              item.dailyComparison >= 0 ? 'positive' : 'negative'
            }`}
          >
            {item.dailyComparison}%{item.dailyComparison >= 0 ? ' ↑' : ' ↓'}
          </span>
          <span className="body-cell">0.7% ↑</span>
        </div>
      ))}
    </div>

    <div className="table-footer">
      <div className="summary-item">
        <span className="summary-label">操作出勤人数</span>
        <span className="summary-value">34人</span>
      </div>
      <div className="summary-item">
        <span className="summary-label">移动出勤人数</span>
        <span className="summary-value">25人</span>
      </div>
      <div className="summary-item">
        <span className="summary-label">出勤率</span>
        <span className="summary-value">87.6%</span>
      </div>
      <div className="summary-item">
        <span className="summary-label">出勤环比</span>
        <span className="summary-value positive">0.7% ↑</span>
      </div>
    </div>
  </section>
);
