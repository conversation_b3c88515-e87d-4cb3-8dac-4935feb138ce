import dayjs from 'dayjs';

/**
 * 人员效能模拟数据服务
 * 用于模拟 /cockpit/kg/personEff/trend 接口
 */
export class PersonnelEfficiencyMockService {
  /**
   * 根据字符串生成一个数字种子
   * @param {string} str 输入字符串
   * @returns {number} 数字种子
   */
  static generateSeed(str) {
    let hash = 0;
    if (!str || str.length === 0) return hash;

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash);
  }
  /**
   * 获取人员效能趋势数据
   * @param {Object} params 请求参数
   * @param {string} params.date 日期 (YYYY-MM-DD 或 YYYY-MM)
   * @param {number} params.type 类型 (0:日维度, 1:月维度)
   * @param {string} params.deptCode 部门代码
   * @returns {Promise<Object>} 响应数据
   */
  static async fetchPersonnelEfficiencyTrend({ date, type, deptCode = '' }) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300));

    const isDaily = type === 0;
    const baseMultiplier = isDaily ? 1 : 30; // 月维度数据约为日维度的30倍

    // 生成基于日期的随机种子，确保相同日期返回相同数据
    const seed = this.generateSeed(date);

    // 趋势数据 (效率卡片数据)
    const trendData = {
      opWeight: this.generateValue(seed, 500, 600) * baseMultiplier,
      opAttendanceEff: this.generateValue(seed + 1, 50, 60),
      mobileAttendanceEff: this.generateValue(seed + 2, 60, 70),
      laborEff: this.generateValue(seed + 3, 20, 30),
      weightRatio: this.generateValue(seed + 4, 1, 2) * (isDaily ? 1 : 3),
      opAttendanceNumber: this.generateValue(seed + 5, 40, 50),
      mobileAttendanceNumber: this.generateValue(seed + 6, 25, 35),
      secAttendanceRate: this.generateValue(seed + 7, 85, 92),
      secAttendanceRatio: this.generateValue(seed + 8, 0.5, 1.5),
    };

    // 图表数据
    const chartData = this.generateChartData(date, type);

    // 组别效率数据
    const groupData = this.generateGroupEfficiencyData(seed);

    return {
      code: 200,
      message: 'success',
      data: {
        trend: trendData,
        chart: chartData,
        group: groupData,
      },
    };
  }

  /**
   * 生成基于字符串的伪随机种子
   */
  static generateSeed(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 基于种子生成范围内的值
   */
  static generateValue(seed, min, max, decimals = 1) {
    const random = (seed % 1000) / 1000;
    const value = min + (max - min) * random;
    return Number(value.toFixed(decimals));
  }

  /**
   * 生成图表数据
   */
  static generateChartData(date, type) {
    const isDaily = type === 0;
    const days = isDaily ? 7 : 30; // 日维度显示7天，月维度显示30天

    const chartData = {
      volumeEfficiency: [],
      attendance: [],
    };

    // 生成时间序列
    const startDate = dayjs(date);

    for (let i = 0; i < days; i++) {
      const currentDate = startDate.subtract(
        days - 1 - i,
        isDaily ? 'day' : 'day',
      );
      const timeStr = currentDate.format(isDaily ? 'MM-DD' : 'MM-DD');
      const seed = this.generateSeed(currentDate.format('YYYY-MM-DD')) + i;

      // 货量和在职劳效数据
      chartData.volumeEfficiency.push(
        {
          name: 'volume',
          time: timeStr,
          value: this.generateValue(seed, 1000, 2500),
        },
        {
          name: 'efficiency',
          time: timeStr,
          value: this.generateValue(seed + 1, 45, 60),
        },
      );

      // 出勤数据
      const opAttendance = this.generateValue(seed + 2, 32, 38, 0);
      const mobileAttendance = this.generateValue(seed + 3, 25, 32, 0);
      const attendanceRate = this.generateValue(seed + 4, 85, 95);

      chartData.attendance.push(
        { name: 'operatingAttendance', time: timeStr, value: opAttendance },
        { name: 'mobileAttendance', time: timeStr, value: mobileAttendance },
        { name: 'attendanceRate', time: timeStr, value: attendanceRate },
      );
    }

    return chartData;
  }

  /**
   * 生成组别效率数据
   */
  static generateGroupEfficiencyData(seed) {
    const groups = ['装卸组1', '装卸组2', '装卸组3', '装卸组4'];

    return groups.map((groupName, index) => ({
      groupName,
      operatingEfficiency: this.generateValue(seed + index * 10, 48, 58),
      dailyComparison: this.generateValue(seed + index * 10 + 1, -2, 2),
    }));
  }

  /**
   * 模拟API调用失败的情况
   */
  static async fetchWithError() {
    await new Promise(resolve => setTimeout(resolve, 300));
    throw new Error('模拟网络错误');
  }
}

/**
 * 人员效能数据服务的简化接口
 * @param {string} date 日期
 * @param {number} type 类型 (0:日维度, 1:月维度)
 * @param {string} deptCode 部门代码
 * @returns {Promise<Object>} 格式化后的数据
 */
/**
 * 人员效能数据服务的简化接口
 * 作为真实 API 的降级方案使用
 * @param {string} date 日期
 * @param {number} type 类型 (0:日维度, 1:月维度)
 * @param {string} deptCode 部门代码
 * @returns {Promise<Object>} 格式化后的数据
 */
export async function getPersonnelEfficiencyData(date, type, deptCode = '') {
  try {
    // 记录 mock 数据请求信息
    console.log(
      `[Mock] 获取人员效能数据，日期: ${date}, 类型: ${type}, 部门代码: ${deptCode}`,
    );

    const response = await PersonnelEfficiencyMockService.fetchPersonnelEfficiencyTrend(
      {
        date,
        type,
        deptCode,
      },
    );

    if (response.code !== 200) {
      throw new Error(response.message || '接口返回错误');
    }

    const { trend, chart, group } = response.data;

    // 如果有 deptCode，可以根据不同的部门代码调整数据
    if (deptCode) {
      // 使用 deptCode 的哈希值作为随机种子，确保同一部门返回相似数据
      const seed = PersonnelEfficiencyMockService.generateSeed(deptCode);
      const multiplier = (seed % 20) / 10 + 0.5; // 生成 0.5 到 2.5 之间的乘数

      // 根据部门调整数据
      trend.opWeight *= multiplier;
      trend.laborEff *= multiplier;
      trend.opAttendanceEff *= multiplier;
      trend.mobileAttendanceEff *= multiplier;
    }

    return {
      success: true,
      data: {
        efficiencyCards: trend,
        volumeEfficiencyChart: chart.volumeEfficiency,
        attendanceChart: chart.attendance,
        groupEfficiencyData: group,
      },
    };
  } catch (error) {
    console.error('获取人员效能数据失败:', error);

    // 返回降级数据
    return {
      success: false,
      error: error.message,
      data: {
        efficiencyCards: {},
        volumeEfficiencyChart: [],
        attendanceChart: [],
        groupEfficiencyData: [],
      },
    };
  }
}
