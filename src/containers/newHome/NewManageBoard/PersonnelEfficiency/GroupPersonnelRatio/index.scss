.group-personnel-ratio {
  .group-table-body-no-data {
    .no-data-root {
      padding-top: 10px;
    }
  }
  .header-title {
    // margin-left: 10px;
    padding-top: 16px;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 600;
  }

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0;
    margin-bottom: 16px;
    // padding-top: 8px;
    align-items: center; // 确保垂直对齐

    .time-granularity-switcher {
      display: flex;
      height: 24px;
      border-radius: 12px;
      background-color: #f5f5f5;

      .granularity-btn {
        padding: 0 14px;
        line-height: 24px;
        border: none;
        color: #606266;
        font-size: 14px;
        border-radius: 12px;
        background-color: inherit;

        &.active {
          background-color: #ffffff;
          color: #cc1b23;
          border-radius: 12px;
          border: 0.5px solid #e7e7e7;
        }
      }
    }

    // .date-selector {
    //   padding: 8px 12px;
    //   font-size: 14px;
    //   color: #333;
    //   font-weight: normal;
    //   font-family: PingFang SC;
    //   border: 1px solid rgba(255, 255, 255, 0.2);

    //   .placeholder {
    //     color: #999;
    //   }
    // }
  }

  .group-table-container {
    // margin: 10px;
    // background-color: #fffbf0;
    border-radius: 8px;
    // padding: 10px;

    .group-table-header {
      display: flex;
      padding: 8px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      font-size: 12px;
      font-weight: 600;
      color: #666;

      .header-cell {
        flex: 1;
        font-size: 11px;
        font-weight: 200;
        text-align: center;

        &:first-child {
          text-align: left;
          padding-left: 10px;
        }

        &:last-child {
          flex: 0 0 12px; // 固定宽度24px，为12px箭头留出空间
          min-width: 12px;
        }
      }
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100px;
      color: #999;
      font-size: 14px;
    }

    .group-table-body {
      max-height: 300px; /* 设置固定最大高度 */
      overflow-y: auto; /* 添加垂直滚动条 */
      scrollbar-width: thin; /* Firefox滚动条样式 */

      /* 自定义滚动条样式 - Chrome/Safari */
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #999;
      }
      .table-row {
        display: flex;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        font-size: 12px;
        color: #000; // 统一设置为黑色

        &.clickable-row {
          cursor: pointer;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: rgba(204, 27, 35, 0.05);

            .right-arrow-icon {
              opacity: 1;
            }
          }

          &:active {
            background-color: rgba(204, 27, 35, 0.1);
          }
        }

        .table-cell {
          flex: 1;
          font-size: 11px;
          font-weight: 300;
          text-align: center;
          color: #000; // 统一设置为黑色

          &:first-child {
            text-align: left;
            padding-left: 10px;
            font-weight: 500;
            color: #000; // 统一设置为黑色
          }

          &.group-name {
            color: #000; // 统一设置为黑色
            font-weight: 500;
          }

          .arrow-icon {
            width: 5px;
            height: 7px;
            margin-left: 2px;
            vertical-align: middle;
          }

          .efficiency-unit {
            color: rgba(153, 153, 153, 1);
            font-weight: 400;
            margin-left: 2px;
          }

          &.arrow-cell {
            flex: 0 0 12px; // 固定宽度24px
            min-width: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .right-arrow-icon {
            width: 12px;
            height: 12px;
            opacity: 0.6;
            transition: opacity 0.2s ease;
          }

          span {
            &.positive {
              color: #000; // 统一设置为黑色
            }

            &.negative {
              color: #000; // 统一设置为黑色
            }
          }
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .no-data-container {
      padding: 20px 0;
      min-height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .view-more-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 0;
    color: #666;
    font-size: 12px;
    cursor: pointer;
  }

  .view-more-container-arrow {
    width: 13px;
    height: 13px;
    margin-left: 4px;
    transition: transform 0.3s ease;

    &.rotate-180 {
      transform: rotate(180deg);
    }
  }
}
