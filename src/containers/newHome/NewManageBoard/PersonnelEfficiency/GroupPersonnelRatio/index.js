import React, { useState, useEffect, useReducer } from 'react';
import { useHistory } from 'react-router-dom';
import dayjs from 'dayjs';
import { DateSelect } from 'src/components/DateSelect';
import { NoData } from 'src/components/NoData';
import axios from 'src/utils/axios';
import arrowBottom from '../../images/arrow_bottom.png';
import arrowUp from '../../images/green_arrow_up.svg';
import arrowRight from '../../images/arrow_right.svg';
import './index.scss';

// 获取组别人效比例数据接口
const getGroupPersonnelRatioData = async (date, type, deptCode) => {
  try {
    const response = await axios({
      url: '/cockpit/kg/personEff/getPersonEffGroup',
      method: 'POST',
      data: {
        date,
        type,
        deptCode,
      },
    });
    return {
      success: true,
      data: {
        groupData: response || [],
      },
    };
  } catch (error) {
    // 错误处理，记录日志
    return {
      success: false,
      data: {
        groupData: [],
      },
    };
  }
};

export const GroupPersonnelRatio = ({ deptCode }) => {
  const history = useHistory();
  const [timeGranularity, setTimeGranularity] = useState('date'); // 'date' or 'month'
  const [selectedDate, setSelectedDate] = useState(
    dayjs()
      .subtract(1, 'day')
      .toDate(),
  );
  const [loading, setLoading] = useState(false);
  const [showAll, setShowAll] = useState(false);

  const [data, dispatchData] = useReducer(
    (state, actions) => ({ ...state, ...actions.payload }),
    {
      groupData: [],
    },
  );

  // 获取组别人数比例数据
  const fetchGroupPersonnelRatioData = async (granularity, date) => {
    setLoading(true);
    try {
      // 格式化日期，根据时间粒度选择不同格式
      const formattedDate = dayjs(date).format(
        granularity === 'date' ? 'YYYY-MM-DD 00:00:00' : 'YYYY-MM-01 00:00:00',
      );

      // 根据时间粒度设置类型参数
      // 0:日维度,1:月维度
      const type = granularity === 'date' ? 0 : 1;

      const result = await getGroupPersonnelRatioData(
        formattedDate,
        type,
        deptCode,
      );

      if (result.success) {
        dispatchData({
          payload: {
            ...result.data,
          },
        });
      }
    } catch (error) {
      // 错误处理
    } finally {
      setLoading(false);
    }
  };

  // 时间粒度变化处理
  const handleTimeGranularityChange = newGranularity => {
    if (newGranularity !== timeGranularity) {
      setTimeGranularity(newGranularity);
      // 时间粒度变化后，useEffect会自动监听并重新请求数据
    }
  };

  // 日期变化处理
  const handleDateChange = date => {
    // 检查选择的日期是否为今天或之后，如果是则不允许选择
    const today = dayjs().startOf('day');
    const selectedDay = dayjs(date).startOf('day');

    if (selectedDay.isSame(today) || selectedDay.isAfter(today)) {
      // 如果选择的是今天或之后的日期，显示提示并不更新状态
      console.warn('不能选择今天及今天之后的日期');
      return;
    }

    setSelectedDate(date);
  };

  // 查看更多处理
  const handleViewMore = () => {
    // 切换显示全部/部分数据
    setShowAll(!showAll);
  };

  // 点击组别行跳转到详情页面
  const handleGroupClick = groupName => {
    // 对组别名称进行URL编码
    const encodedGroupName = encodeURIComponent(groupName);
    // 根据时间粒度设置类型参数：0:日维度,1:月维度
    const type = timeGranularity === 'date' ? 0 : 1;
    // 格式化选择的日期作为URL参数
    const formattedDate = dayjs(selectedDate).format(
      timeGranularity === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM',
    );
    history.push(
      `/group-personnel-detail/${encodedGroupName}/${type}?date=${formattedDate}`,
    );
  };

  // 根据时间粒度获取表格配置
  const getTableConfig = () => {
    if (timeGranularity === 'date') {
      // 日维度配置
      return {
        headers: ['装卸组别', '总货量', '当日效能', '周同比', '日环比', ''],
      };
    }
    // 月维度配置
    return {
      headers: ['装卸组别', '当月总货量', '当月效能', '货量月环比', ''],
    };
  };

  // 日期、时间粒度或deptCode变化时获取数据
  useEffect(() => {
    if (deptCode) {
      fetchGroupPersonnelRatioData(timeGranularity, selectedDate);
    }
  }, [timeGranularity, selectedDate, deptCode]);

  const tableConfig = getTableConfig();

  return (
    <div className="group-personnel-ratio">
      <div className="header-title">各组别累计人效完成 (T-1)</div>
      <div className="header-section">
        <div className="time-granularity-switcher">
          <button
            type="button"
            className={`granularity-btn ${
              timeGranularity === 'date' ? 'active' : ''
            }`}
            onClick={() => handleTimeGranularityChange('date')}
          >
            日
          </button>
          <button
            type="button"
            className={`granularity-btn ${
              timeGranularity === 'month' ? 'active' : ''
            }`}
            onClick={() => handleTimeGranularityChange('month')}
          >
            月
          </button>
        </div>
        <div className="date-selector">
          <DateSelect
            mode={timeGranularity}
            value={selectedDate}
            onChange={handleDateChange}
            placeholder={timeGranularity === 'date' ? '选择日期' : '选择月份'}
            format={timeGranularity === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM'}
            maxDate={dayjs()
              .subtract(1, 'day')
              .toDate()}
          />
        </div>
      </div>

      {/* 组别数据表格 */}
      <div className="group-table-container">
        <div className="group-table-header">
          {tableConfig.headers.map(header => (
            <div key={header} className="header-cell">
              {header}
            </div>
          ))}
        </div>

        {loading ? (
          <div className="loading-container">加载中...</div>
        ) : data.groupData.length > 0 ? (
          <>
            <div className="group-table-body">
              {data.groupData
                .slice(0, showAll ? data.groupData.length : 3)
                .map(item => (
                  <div
                    className="table-row clickable-row"
                    key={item.orgName || item.groupName}
                    onClick={() => handleGroupClick(item.orgName)}
                  >
                    {/* 装卸组别 */}
                    <div className="table-cell group-name">{item.orgName}</div>

                    {timeGranularity === 'date' ? (
                      // 日维度：装卸组别 总货量 当月效能 周同比 日环比
                      <>
                        {/* 总货量 */}
                        <div className="table-cell">
                          {Number(item.totalWeight || 0).toFixed(2)}T
                        </div>
                        {/* 当月效能 */}
                        <div className="table-cell">
                          {Number(item.effMtd || 0).toFixed(2)}
                          <span className="efficiency-unit">吨/人</span>
                        </div>
                        {/* 周同比 */}
                        <div className="table-cell">
                          {(item.weekTotalWeightRatio * 100 || 0).toFixed(2)}%{' '}
                          {(item.weekTotalWeightRatio * 100 || 0) > 0 && (
                            <img
                              src={arrowUp}
                              className="arrow-icon"
                              alt="上涨"
                            />
                          )}
                        </div>
                        {/* 日环比 */}
                        <div className="table-cell">
                          {(item.totalWeightRatio * 100 || 0).toFixed(2)}%{' '}
                          {(item.totalWeightRatio * 100 || 0) > 0 && (
                            <img
                              src={arrowUp}
                              className="arrow-icon"
                              alt="上涨"
                            />
                          )}
                        </div>
                        {/* 向右箭头 */}
                        <div className="table-cell arrow-cell">
                          <img
                            src={arrowRight}
                            className="right-arrow-icon"
                            alt="查看详情"
                          />
                        </div>
                      </>
                    ) : (
                      // 月维度：装卸组别 当月总货量 当月效能 货量月环比
                      <>
                        {/* 当月总货量 */}
                        <div className="table-cell">
                          {Number(item.totalWeightMtd || 0).toFixed(2)}T
                        </div>
                        {/* 当月效能 */}
                        <div className="table-cell">
                          {Number(item.effMtd || 0).toFixed(2)}
                          <span className="efficiency-unit">吨/人</span>
                        </div>
                        {/* 货量月环比 */}
                        <div className="table-cell">
                          {(item.totalWeightMtdRatio * 100 || 0).toFixed(2)}%{' '}
                          {(item.totalWeightMtdRatio * 100 || 0) > 0 && (
                            <img
                              src={arrowUp}
                              className="arrow-icon"
                              alt="上涨"
                            />
                          )}
                        </div>
                        {/* 向右箭头 */}
                        <div className="table-cell arrow-cell">
                          <img
                            src={arrowRight}
                            className="right-arrow-icon"
                            alt="查看详情"
                          />
                        </div>
                      </>
                    )}
                  </div>
                ))}
            </div>

            {data.groupData.length > 3 && (
              <div className="view-more-container" onClick={handleViewMore}>
                {showAll ? '收起' : '展开更多'}
                <img
                  src={arrowBottom}
                  className={`view-more-container-arrow ${
                    showAll ? 'rotate-180' : ''
                  }`}
                />
              </div>
            )}
          </>
        ) : (
          <div className="no-data-container group-table-body-no-data">
            <NoData small tooltip="暂无数据" />
          </div>
        )}
      </div>
    </div>
  );
};
