import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import dayjs from 'dayjs';
import axios from 'src/utils/axios';
import { DateSelect } from 'src/components/DateSelect';
import Header from '../../components/Header';
import { REFRESH_STATE, LOAD_STATE } from '../../constants';
import TableWithHeader from '../../components/TableWithHeader';
import { getTableConfig } from '../../components/TableWithHeader/tableConfigs';
import './index.scss';

const pageSize = 20;

// 数据格式转换函数
function convertDataToTableFormat(data) {
  if (!data || !Array.isArray(data)) return [];
  // 格式化日期显示
  const formatDate = dateStr => {
    if (!dateStr) return '';
    return dayjs(dateStr).format('MM-DD HH:mm');
  };

  return data.map(item => [
    // formatDate(item.time), // 时间
    item.loginid || '-', // 员工编码
    item.username || '-', // 员工姓名
    item.positionName || '-', // 岗位
    // item.loadGroup || '-', // 装卸组别
    Number(item.totalWeight || 0).toFixed(1), // 当日总货量
    Number(item.totalWeightMtd || 0).toFixed(1), // 当月总货量
    Number(item.overweightInctMtd || 0), // 当月偷重偷方上报票数
    Number(item.attendanceDays || 0), // 当月出勤天数
    Number(item.effMtd || 0).toFixed(1), // 当月效能
  ]);
}

// 人员效能详情列表页面
class PersonnelEfficiencyDetail extends React.PureComponent {
  constructor(props) {
    super(props);

    // 先解析URL参数获取初始状态
    const initialState = this.getInitialStateFromUrl(props);

    this.state = {
      ...initialState,
      // 添加一个key来强制TableWithHeader重新渲染
      tableKey: Date.now(),
    };

    this.loadPersonnelEfficiencyDetail = this.loadPersonnelEfficiencyDetail.bind(
      this,
    );
    this.refreshPersonnelEfficiencyDetail = this.refreshPersonnelEfficiencyDetail.bind(
      this,
    );
    this.handleDateChange = this.handleDateChange.bind(this);
  }

  // 从URL参数获取初始状态
  getInitialStateFromUrl(props) {
    const { location } = props;
    const searchParams = new URLSearchParams(location.search);

    const dateParam = searchParams.get('date');
    const zoneCodeParam = searchParams.get('zoneCode');

    if (dateParam) {
      const passedDate = dayjs(dateParam).toDate();

      return {
        selectedDate: passedDate,
        passedZoneCode: zoneCodeParam,
      };
    }

    // 如果没有URL参数，使用默认值（昨天）
    const defaultDate = dayjs()
      .subtract(1, 'day')
      .toDate();

    return {
      selectedDate: defaultDate,
    };
  }

  async componentDidMount() {
    // 初始化数据加载
    // URL参数已经在constructor中解析并设置到初始状态
  }

  // 日期变更处理
  handleDateChange(date) {
    if (date) {
      // 检查日期是否真的发生了变化，避免无限循环
      const { selectedDate } = this.state;

      if (!dayjs(date).isSame(dayjs(selectedDate), 'day')) {
        this.setState({
          selectedDate: date,
          // 更新tableKey来强制TableWithHeader重新渲染和重新加载数据
          tableKey: Date.now(),
        });
      }
    }
  }

  // 获取人员效能详情数据
  async fetchPersonnelEfficiencyDetailData(pageNum = 1) {
    const { zoneCode } = this.props;
    const { selectedDate, passedZoneCode } = this.state;

    // 优先使用URL参数传入的zoneCode，否则使用props中的zoneCode
    const deptCode = passedZoneCode || zoneCode;

    try {
      // 构造请求参数 - 获取指定日期的详情数据
      const formattedDate = dayjs(selectedDate).format('YYYY-MM-DD');
      const formattedStartDate = `${formattedDate} 00:00:00`;
      const formattedEndDate = `${formattedDate} 23:59:59`;

      const response = await axios({
        url: '/cockpit/kg/personEff/getPersonEffExceptionDetail',
        method: 'POST',
        data: {
          startDate: formattedStartDate,
          endDate: formattedEndDate,
          deptCode,
          pageNum,
          pageSize,
          // 异常类型：,3天异常,低于8T
          exceptionType: '3天异常',
        },
      });

      return response;
    } catch (error) {
      console.error('获取人员效能详情数据失败:', error);
    }
  }

  // 下拉刷新数据
  refreshPersonnelEfficiencyDetail({
    setCurrentPage,
    setRefreshing,
    setDatas,
  }) {
    setRefreshing(REFRESH_STATE.loading);

    this.fetchPersonnelEfficiencyDetailData(1)
      .then(res => {
        setCurrentPage(1);
        // 转换数据格式为表格格式
        const formattedData = convertDataToTableFormat(res.rows || res || []);
        setDatas(formattedData);
        setRefreshing(REFRESH_STATE.success);
      })
      .catch(() => {
        setRefreshing(REFRESH_STATE.failure);
      });
  }

  // 上拉加载更多数据
  // 注意：此接口不支持分页，所以直接设置为完成状态，禁用触底加载
  loadPersonnelEfficiencyDetail({ setLoading }) {
    // 由于接口不支持分页，直接设置为完成状态，避免无限触底加载
    setLoading(LOAD_STATE.complete);
  }

  render() {
    const { selectedDate, tableKey } = this.state;
    const {
      refreshPersonnelEfficiencyDetail,
      loadPersonnelEfficiencyDetail,
    } = this;

    // 使用配置文件获取表格配置
    const tableConfig = getTableConfig('threeDaysNoWeightDetail');
    const {
      headers,
      columnWidths,
      headerFontSizes,
      cellFontSizes,
    } = tableConfig;

    return (
      <div className="personnel-efficiency-detail-wrap">
        <Header {...this.props} title="3天无货量详情" noFilter />

        {/* 日期选择器区域 */}
        <div className="date-selector-section">
          <div className="section-title">3天无货量详情</div>
          <div className="date-selector">
            <DateSelect
              mode="date"
              value={selectedDate}
              onChange={this.handleDateChange}
              placeholder="选择日期"
              maxDate={new Date()} // 最晚只能选到今天
            />
          </div>
        </div>

        <div className="table-container">
          <TableWithHeader
            key={tableKey}
            headers={headers}
            columnWidths={columnWidths}
            headerFontSizes={headerFontSizes}
            cellFontSizes={cellFontSizes}
            dataSource={[]}
            refreshing={0}
            loading={0}
            load={loadPersonnelEfficiencyDetail}
            refresh={refreshPersonnelEfficiencyDetail}
            className="personnel-efficiency-table"
          />
        </div>
      </div>
    );
  }
}

function mapStateToProps(state) {
  return {
    batchCode: state.global.batchCode,
    zoneCode: state.global.zoneCode,
    planBeginTm: state.global.planBeginTm,
    workDate: state.global.workDate,
  };
}

export default connect(mapStateToProps)(withRouter(PersonnelEfficiencyDetail));
