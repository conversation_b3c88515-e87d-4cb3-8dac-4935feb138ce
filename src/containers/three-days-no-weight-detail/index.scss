.full-screen {
  .personnel-efficiency-detail-wrap {
    .date-selector-section {
      top: calc(48px + var(--sat));
    }
    .table-container {
      padding-top: calc(98px + var(--sat));
      // height: calc(100vh - calc(98px + var(--sat)));

      .table-body {
        height: calc(
          100vh - calc(153px + var(--sat))
        ); // 为底部滚动条预留20px空间
      }
    }
  }
}
.iphone-fit {
  .personnel-efficiency-detail-wrap {
    .date-selector-section {
      top: 68px;
    }
    .table-container {
      padding-top: 118px;
      // height: calc(100vh - 118px);

      .table-body {
        height: calc(100vh - 173px); // 为底部滚动条预留20px空间
      }
    }
  }
}
.personnel-efficiency-detail-wrap {
  background: #fff;
  width: 100%;
  height: 100vh;
  overflow: hidden; // 外层容器不滚动
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
    width: 0;
  }

  .date-selector-section {
    position: fixed;
    top: 48px;
    z-index: 11;
    background: #ffffff;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    padding-left: 15px;
    padding-right: 15px;
    border-bottom: 0.5px solid #f2f2f2;

    .section-title {
      font-family: PingFangSC-Medium, sans-serif;
      font-size: 16px;
      color: #333333;
      font-weight: 500;
    }

    .date-selector {
      flex-shrink: 0;
    }
  }

  .table-container {
    // height: calc(100vh - 98px);
    padding-top: 98px;
    overflow: hidden; // 外层容器不滚动，让内部的 PullDownList 处理滚动
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
      display: none;
    }

    .table-header {
      position: sticky; // 表头在垂直滚动时保持固定
      top: 0;
      z-index: 10;
      background: #ffffff;
      height: 35px;
      border-top: 0.5px solid #f2f2f2;

      .table-row {
        display: flex;
        align-items: center;
        height: 100%;
        min-width: 800px; // 确保所有列都能显示
        padding-left: 15px;
        padding-right: 15px;

        > div {
          text-align: center;
          font-family: PingFangSC-Regular, sans-serif;
          font-size: 11px;
          color: #888888;
          white-space: nowrap;
          flex-shrink: 0;
        }

        > div:nth-child(1) {
          // 员工编码
          width: 70px;
        }
        > div:nth-child(2) {
          // 员工姓名
          width: 70px;
        }
        > div:nth-child(3) {
          // 岗位
          width: 50px;
        }
        > div:nth-child(4) {
          // 当日总货量
          width: 80px;
        }
        > div:nth-child(5) {
          // 当月总货量
          width: 80px;
        }
        > div:nth-child(6) {
          // 当月偷重偷方上报票数
          width: 130px;
        }
        > div:nth-child(7) {
          // 当月出勤天数
          width: 80px;
        }
        > div:nth-child(8) {
          // 当月效能
          width: 70px;
        }
      }
    }

    .table-body {
      height: calc(100vh - 153px); // 为底部滚动条预留20px空间
      overflow: hidden; // 外层不滚动，让 PullDownList 处理滚动
      -webkit-overflow-scrolling: touch;
      &::-webkit-scrollbar {
        display: none;
      }

      // 确保 PullDownList 组件填充整个可用空间
      .ontheway-list {
        height: 100% !important;

        .kymui-pull {
          height: 100%;
          overflow: auto;
          -webkit-overflow-scrolling: touch;

          .kymui-pull__content {
            min-height: 100%;
            display: flex;
            flex-direction: column;
          }

          .kymui-pull__body {
            flex: 1;
            min-height: 0; // 允许内容收缩
          }
        }
      }

      // 当没有数据时的空状态样式
      .ontheway-list:empty::after {
        content: '';
        height: 100%;
        width: 100%;
        background: #f2f2f2;
      }

      .personnel-efficiency-item {
        background: #fff;
        height: 50px;
        margin-top: 1px;

        .table-row {
          display: flex;
          align-items: center;
          height: 100%;
          min-width: 800px; // 与表头保持一致
          padding-left: 15px;
          padding-right: 15px;

          > div {
            font-family: DINAlternate-Bold, sans-serif;
            font-size: 12px;
            color: #000000;
            line-height: 15px;
            text-align: center;
            white-space: nowrap;
            flex-shrink: 0;
          }

          div:nth-child(1) {
            // 员工编码
            width: 70px;
          }
          div:nth-child(2) {
            // 员工姓名
            width: 70px;
          }
          div:nth-child(3) {
            // 岗位
            width: 50px;
          }
          div:nth-child(4) {
            // 当日总货量
            width: 80px;
          }
          div:nth-child(5) {
            // 当月总货量
            width: 80px;
          }
          div:nth-child(6) {
            // 当月偷重偷方上报票数
            width: 130px;
            font-size: 10px;
          }
          div:nth-child(7) {
            // 当月出勤天数
            width: 80px;
          }
          div:nth-child(8) {
            // 当月效能
            width: 70px;
          }
        }
      }
    }
  }
}
