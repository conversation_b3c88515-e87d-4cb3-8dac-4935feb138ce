import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import dayjs from 'dayjs';
import axios from 'src/utils/axios';
import { DateSelect } from 'src/components/DateSelect';
import Header from '../../components/Header';
import { REFRESH_STATE, LOAD_STATE } from '../../constants';
import TableWithHeader from '../../components/TableWithHeader';
import { getTableConfig } from '../../components/TableWithHeader/tableConfigs';
import './index.scss';

const pageSize = 20;

// 数据格式转换函数
function convertDataToTableFormat(data, type) {
  if (!data || !Array.isArray(data)) return [];

  // 格式化日期显示
  const formatDate = dateStr => {
    if (!dateStr) return '';
    const date = dayjs(dateStr);
    return type === 0 ? date.format('MM-DD') : date.format('MM月');
  };

  return data.map(item => {
    if (type === 0) {
      // 日维度
      return [
        formatDate(item.date), // 日期
        Number(item.weight || 0).toFixed(1), // 货量(吨) - 修改为使用 weight 字段
        Number(item.opWeight || 0).toFixed(1), // 日操作货量 - 修改为使用 opWeight 字段
        Number(item.opAttendanceEff || 0).toFixed(1), // 自有操作员出勤劳效
        Number(item.mobileWeight || 0).toFixed(1), // 日移动货量
        Number((item.mobileAttendanceRate || 0) * 100).toFixed(1) + '%', // 移动出勤率
      ];
    } else {
      // 月维度
      return [
        formatDate(item.date), // 日期
        Number(item.monthWeight || 0).toFixed(1), // 货量(吨)
        Number(item.monthOpWeight || 0).toFixed(1), // 月操作货量
        Number(item.opAttendanceEffMtd || 0).toFixed(1), // 自有操作员出勤劳效
        Number(item.monthMobileWeight || 0).toFixed(1), // 月移动货量
      ];
    }
  });
}

// 人员效能详情列表页面
class PersonnelEfficiencyDetail extends React.PureComponent {
  constructor(props) {
    super(props);

    // 先解析URL参数获取初始状态
    const initialState = this.getInitialStateFromUrl(props);

    this.state = {
      ...initialState,
      // 添加一个key来强制TableWithHeader重新渲染
      tableKey: Date.now(),
    };

    this.loadPersonnelEfficiencyDetail = this.loadPersonnelEfficiencyDetail.bind(
      this,
    );
    this.refreshPersonnelEfficiencyDetail = this.refreshPersonnelEfficiencyDetail.bind(
      this,
    );
    this.handleDateChange = this.handleDateChange.bind(this);
  }

  // 从URL参数解析初始状态
  getInitialStateFromUrl(props) {
    const urlParams = new URLSearchParams(props.location.search);
    const type = urlParams.get('type') || '1'; // 默认为月维度
    const dateParam = urlParams.get('date');
    const timeGranularity = urlParams.get('timeGranularity') || 'month';
    const passedZoneCode = urlParams.get('zoneCode'); // 从URL获取zoneCode

    // 根据类型设置默认日期
    let defaultDate;
    if (type === '0') {
      // 日维度
      defaultDate = dateParam
        ? dayjs(dateParam).toDate()
        : new Date(Date.now() - 86400000);
    } else {
      // 月维度
      defaultDate = dateParam
        ? dayjs(dateParam)
            .startOf('month')
            .toDate()
        : dayjs()
            .startOf('month')
            .toDate();
    }

    return {
      data: [],
      currentDate: defaultDate,
      selectedDate: defaultDate,
      type: parseInt(type, 10), // 0:日维度, 1:月维度
      timeGranularity,
      passedZoneCode, // 保存从URL传入的zoneCode
    };
  }

  // 日期变更处理
  handleDateChange(date) {
    if (date) {
      // 检查日期是否真的发生了变化，避免无限循环
      const { selectedDate } = this.state;

      if (!dayjs(date).isSame(dayjs(selectedDate), 'day')) {
        this.setState({
          selectedDate: date,
          currentDate: date,
          // 更新tableKey来强制TableWithHeader重新渲染和重新加载数据
          tableKey: Date.now(),
        });
      }
    }
  }

  // 获取人员效能详情数据 - 适配TableWithHeader的分页加载
  async fetchPersonnelEfficiencyDetailData(pageNum = 1) {
    const { zoneCode } = this.props;
    const { selectedDate, type, passedZoneCode } = this.state;

    // 优先使用URL参数传入的zoneCode，否则使用props中的zoneCode
    const deptCode = passedZoneCode || zoneCode;

    try {
      // 根据类型格式化日期
      const formattedDate =
        type === 0
          ? dayjs(selectedDate).format('YYYY-MM-DD 00:00:00') // 日维度
          : dayjs(selectedDate)
              .startOf('month')
              .format('YYYY-MM-DD 00:00:00'); // 月维度

      const response = await axios({
        url: '/cockpit/kg/personEff/trendChart',
        method: 'POST',
        data: {
          date: formattedDate,
          deptCode,
          pageNum,
          pageSize,
          type,
        },
      });

      // 根据API响应结构提取数据
      let processedData = [];

      if (Array.isArray(response)) {
        processedData = response;
      } else if (response && typeof response === 'object') {
        if (response.rows && Array.isArray(response.rows)) {
          processedData = response.rows;
        } else if (response.data) {
          if (Array.isArray(response.data)) {
            processedData = response.data;
          } else if (response.data.rows && Array.isArray(response.data.rows)) {
            processedData = response.data.rows;
          }
        }
      }

      // 转换数据格式为TableWithHeader需要的格式
      const tableData = convertDataToTableFormat(processedData, type);

      return {
        data: tableData,
        hasMore: processedData.length >= pageSize, // 判断是否还有更多数据
      };
    } catch (error) {
      console.error('获取人员效能详情数据失败:', error);
      return {
        data: [],
        hasMore: false,
      };
    }
  }

  // 刷新数据 - 适配TableWithHeader
  async refreshPersonnelEfficiencyDetail({
    setLoading,
    setCurrentPage,
    currentPage,
    datas,
    setRefreshing,
    setDatas,
  }) {
    try {
      setRefreshing(REFRESH_STATE.loading);

      // 重置到第一页
      const result = await this.fetchPersonnelEfficiencyDetailData(1);

      if (result && result.data) {
        setDatas(result.data);
        setCurrentPage(1);
        setRefreshing(REFRESH_STATE.success);
      } else {
        setRefreshing(REFRESH_STATE.failure);
      }
    } catch (error) {
      console.error('刷新数据失败:', error);
      setRefreshing(REFRESH_STATE.failure);
    }
  }

  // 加载更多数据 - 适配TableWithHeader
  async loadPersonnelEfficiencyDetail({
    setLoading,
    loading,
    setCurrentPage,
    datas,
    currentPage,
    setRefreshing,
    setDatas,
  }) {
    try {
      if (loading === LOAD_STATE.loading) return;

      setLoading(LOAD_STATE.loading);

      const nextPage = currentPage + 1;
      const result = await this.fetchPersonnelEfficiencyDetailData(nextPage);

      if (result && result.data && result.data.length > 0) {
        // 合并新数据
        const newDatas = [...datas, ...result.data];
        setDatas(newDatas);
        setCurrentPage(nextPage);

        // 根据是否还有更多数据设置加载状态
        if (result.hasMore) {
          setLoading(LOAD_STATE.success);
        } else {
          setLoading(LOAD_STATE.complete);
        }
      } else {
        setLoading(LOAD_STATE.complete);
      }
    } catch (error) {
      console.error('加载更多数据失败:', error);
      setLoading(LOAD_STATE.failure);
    }
  }

  render() {
    const { selectedDate, tableKey, type } = this.state;
    const {
      refreshPersonnelEfficiencyDetail,
      loadPersonnelEfficiencyDetail,
    } = this;

    // 根据类型设置标题和表头
    const isDay = type === 0;
    const sectionTitle = isDay ? '日效能数据' : '月效能数据';
    const dateMode = isDay ? 'date' : 'month';
    const placeholder = isDay ? '选择日期' : '选择月份';
    const format = isDay ? 'YYYY-MM-DD' : 'YYYY-MM';
    const maxDate = isDay
      ? dayjs()
          .subtract(1, 'day')
          .toDate()
      : dayjs()
          .startOf('month')
          .toDate();

    // 使用配置文件获取表格配置
    const tableConfig = getTableConfig(
      'personnelEfficiencyDetail',
      isDay ? 'day' : 'month',
    );
    const {
      headers,
      columnWidths,
      headerFontSizes,
      cellFontSizes,
    } = tableConfig;

    // 调试信息
    console.log('Table Config:', {
      headers,
      columnWidths,
      headerFontSizes,
      cellFontSizes,
    });

    return (
      <div className="personnel-efficiency-detail-wrap">
        <Header {...this.props} title="效能趋势详情" noFilter />

        {/* 日期选择器区域 */}
        <div className="date-selector-section">
          <div className="section-title">{sectionTitle}</div>
          <div className="date-selector">
            <DateSelect
              mode={dateMode}
              value={selectedDate}
              onChange={this.handleDateChange}
              placeholder={placeholder}
              format={format}
              maxDate={maxDate}
            />
          </div>
        </div>

        <div className="table-container">
          <TableWithHeader
            key={tableKey}
            headers={headers}
            columnWidths={columnWidths}
            headerFontSizes={headerFontSizes}
            cellFontSizes={cellFontSizes}
            dataSource={[]}
            refreshing={0}
            loading={0}
            load={loadPersonnelEfficiencyDetail}
            refresh={refreshPersonnelEfficiencyDetail}
            className="personnel-efficiency-table"
          />
        </div>
      </div>
    );
  }
}

function mapStateToProps(state) {
  return {
    batchCode: state.global.batchCode,
    zoneCode: state.global.zoneCode,
    planBeginTm: state.global.planBeginTm,
    workDate: state.global.workDate,
  };
}

export default connect(mapStateToProps)(withRouter(PersonnelEfficiencyDetail));
