.full-screen {
  .personnel-efficiency-detail-wrap {
    .date-selector-section {
      top: calc(48px + var(--sat));
    }
    .table-container {
      padding-top: calc(98px + var(--sat));

      .table-body {
        height: calc(
          100vh - calc(153px + var(--sat))
        ); // 为底部滚动条预留20px空间
      }
    }
  }
}
.iphone-fit {
  .personnel-efficiency-detail-wrap {
    .date-selector-section {
      top: 68px;
    }
    .table-container {
      padding-top: 118px;

      .table-body {
        height: calc(100vh - 173px); // 为底部滚动条预留20px空间
      }
    }
  }
}
.personnel-efficiency-detail-wrap {
  background: #fff;
  width: 100%;
  height: 100vh;
  overflow: hidden; // 外层容器不滚动
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
    width: 0;
  }

  .date-selector-section {
    position: fixed;
    top: 48px;
    z-index: 11;
    background: #ffffff;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    padding-left: 15px;
    padding-right: 15px;
    border-bottom: 0.5px solid #f2f2f2;

    .section-title {
      font-family: PingFangSC-Medium, sans-serif;
      font-size: 16px;
      color: #333333;
      font-weight: 500;
    }

    .date-selector {
      flex-shrink: 0;
    }
  }

  .table-container {
    padding-top: 98px;
    overflow: hidden; // 外层容器不滚动，让内部的 TableWithHeader 处理滚动
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
      display: none;
    }

    .table-with-header {
      .table-header {
        position: sticky; // 表头在垂直滚动时保持固定
        top: 0;
        z-index: 10;
        background: #ffffff;
        height: 35px;
        border-top: 0.5px solid #f2f2f2;

        .table-header-content {
          display: flex;
          align-items: center;
          height: 100%;
          min-width: 510px; // 确保所有列都能显示 (日维度6列)
          padding-left: 15px;
          padding-right: 15px;

          .table-header-cell {
            text-align: center;
            font-family: PingFangSC-Regular, sans-serif;
            // font-size: 11px; // 移除固定字体大小，使用配置文件中的字体大小
            color: #888888;
            white-space: nowrap;
            flex-shrink: 0;
          }
        }
      }

      .table-body {
        height: calc(100vh - 153px); // 为底部滚动条预留20px空间
        overflow: hidden; // 外层不滚动，让内部组件处理滚动
        -webkit-overflow-scrolling: touch;
        &::-webkit-scrollbar {
          display: none;
        }

        .table-with-header-row {
          background: #fff;
          height: 50px;
          margin-top: 1px;

          .table-row-content {
            display: flex;
            align-items: center;
            height: 100%;
            min-width: 510px; // 与表头保持一致
            padding-left: 15px;
            padding-right: 15px;

            .table-cell {
              font-family: DINAlternate-Bold, sans-serif;
              // font-size: 12px; // 移除固定字体大小，使用配置文件中的字体大小
              color: #000000;
              line-height: 15px;
              text-align: center;
              white-space: nowrap;
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }
}
