/* eslint-disable consistent-return */
import axios from 'axios';
import { Toast } from 'kymui';
import { getBaseURL } from './utils';
import { getToken } from './getToken';

const TIMEOUT = 20000;
const kzAxios = axios.create({
  baseURL: getBaseURL(),
  timeout: TIMEOUT,
  withCredentials: true,

});

kzAxios.defaults.headers.post['Content-Type'] =
  'application/x-www-form-urlencoded;charset=UTF-8';

/**
 * 请求拦截器
 */
kzAxios.interceptors.request.use(async config => {
  // 添加token到请求头，如果外部已传入token则不覆盖
  const token = getToken();
  if (token && !config.headers.token) {
    config.headers.token = token;
  }
  config.timeout = TIMEOUT;
  return config;
});

/**
 * 响应拦截器
 */
kzAxios.interceptors.response.use(
  res => {
    if (!res.data.success) {
      return Promise.reject(res.data);
    }
    return res.data.obj;
  },
  async error => {
    console.log("error = ",error?.response)
    const msg = error?.response?.data?.errorMessage || '系统出错了，请稍后再试';
    Toast.show(msg, 2000);
    return Promise.reject(error);

  },
);

export default function newAxios(...args) {
  return new Promise((resolve, reject) => {
    if (args[0].contentType) {
      kzAxios.defaults.headers.post['Content-Type'] = args[0].contentType;
    } else {
      kzAxios.defaults.headers.post['Content-Type'] =
        'application/x-www-form-urlencoded;charset=UTF-8';
    }
    if (args[0].responseType) {
      kzAxios.defaults.headers.post.responseType = args[0].responseType;
    }
    kzAxios
      .call(this, ...args)
      .then(_res => {
        resolve(_res);
      })
      .catch(err => {
        reject(err);
      });
  });
}


