import React from 'react';

export interface EmptyStateProps {
  /**
   * 自定义样式类名
   */
  className?: string;
  
  /**
   * 自定义样式对象
   */
  style?: React.CSSProperties;
  
  /**
   * 显示的文本内容
   * @default '暂无数据'
   */
  text?: string;
  
  /**
   * 图片路径
   */
  image?: string;
  
  /**
   * 自定义内容，会覆盖 text 参数
   */
  children?: React.ReactNode;
}

/**
 * 通用缺省页组件
 */
export declare const EmptyState: React.FC<EmptyStateProps>;

export default EmptyState;
