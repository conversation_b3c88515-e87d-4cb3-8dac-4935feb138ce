import React from 'react';
import classNames from 'classnames';
import './index.scss';
import emptyStateImage from './images/empty_icon.png';

/**
 * 通用缺省页组件
 * @param {{
 *   className?: string;
 *   style?: React.CSSProperties;
 *   text?: string;
 *   image?: string;
 *   children?: React.ReactNode;
 * }} props 组件参数
 */
export const EmptyState = ({
  className = '',
  style = {},
  text = '暂无数据',
  image = emptyStateImage, // 使用默认图片
  children,
}) => {
  return (
    <div className={classNames('empty-state-root', className)} style={style}>
      <div className="empty-state-container">
        <div className="empty-state-content">
          {image && (
            <div className="empty-state-image">
              <img src={image} alt="暂无数据" />
            </div>
          )}
          <div className="empty-state-text">{children || text}</div>
        </div>
      </div>
    </div>
  );
};

export default EmptyState;
