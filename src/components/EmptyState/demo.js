import React from 'react';
import { EmptyState } from './index';

/**
 * EmptyState 组件使用示例
 */
export const EmptyStateDemo = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h2>EmptyState 组件使用示例</h2>

      {/* 基本使用 */}
      <div style={{ marginBottom: '40px' }}>
        <h3>1. 基本使用（带默认图片）</h3>
        <div style={{ height: '300px', border: '1px solid #ddd' }}>
          <EmptyState />
        </div>
      </div>

      {/* 自定义文本 */}
      <div style={{ marginBottom: '40px' }}>
        <h3>2. 自定义文本</h3>
        <div style={{ height: '300px', border: '1px solid #ddd' }}>
          <EmptyState text="暂无相关数据，请稍后再试" />
        </div>
      </div>

      {/* 小尺寸模式 */}
      <div style={{ marginBottom: '40px' }}>
        <h3>3. 小尺寸模式</h3>
        <div style={{ height: '200px', border: '1px solid #ddd' }}>
          <EmptyState className="small" text="暂无数据" />
        </div>
      </div>

      {/* 不显示图片 */}
      <div style={{ marginBottom: '40px' }}>
        <h3>4. 不显示图片</h3>
        <div style={{ height: '300px', border: '1px solid #ddd' }}>
          <EmptyState image={null} text="仅显示文字的缺省页" />
        </div>
      </div>

      {/* 自定义内容 */}
      <div style={{ marginBottom: '40px' }}>
        <h3>5. 自定义内容</h3>
        <div style={{ height: '300px', border: '1px solid #ddd' }}>
          <EmptyState>
            <div>
              <div style={{ fontSize: '18px', marginBottom: '10px' }}>
                暂无数据
              </div>
              <div style={{ fontSize: '14px', color: '#999' }}>
                请检查网络连接或稍后重试
              </div>
            </div>
          </EmptyState>
        </div>
      </div>

      {/* 自定义样式 */}
      <div style={{ marginBottom: '40px' }}>
        <h3>6. 自定义样式</h3>
        <div style={{ height: '250px', border: '1px solid #ddd' }}>
          <EmptyState
            style={{ height: '100%', backgroundColor: '#f0f8ff' }}
            text="自定义背景色的缺省页"
          />
        </div>
      </div>
    </div>
  );
};

export default EmptyStateDemo;
