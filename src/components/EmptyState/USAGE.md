# EmptyState 组件使用指南

## 快速开始

### 1. 基本引入

```javascript
import { EmptyState } from 'src/components/EmptyState';
```

### 2. 基本使用

```jsx
// 最简单的使用方式（自动显示默认图片）
<EmptyState />

// 自定义文本
<EmptyState text="暂无相关数据" />

// 小尺寸模式
<EmptyState className="small" />

// 不显示图片
<EmptyState image={null} text="仅显示文字" />
```

### 3. 在列表页面中使用

```jsx
import React from 'react';
import { EmptyState } from 'src/components/EmptyState';

const ListPage = ({ dataList, loading }) => {
  if (loading) {
    return <div>加载中...</div>;
  }

  if (!dataList || dataList.length === 0) {
    return <EmptyState text="暂无数据" />;
  }

  return (
    <div>
      {dataList.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
};
```

### 4. 在表格组件中使用

```jsx
import { EmptyState } from 'src/components/EmptyState';
import TableWithHeader from 'src/components/TableWithHeader';

const TablePage = ({ dataSource }) => {
  if (!dataSource || dataSource.length === 0) {
    return <EmptyState className="small" text="暂无表格数据" />;
  }

  return (
    <TableWithHeader
      headers={headers}
      dataSource={dataSource}
      // ... 其他属性
    />
  );
};
```

## 图片资源

组件已经内置了默认的缺省页图片 (`empty_icon.png`)，会自动显示。

### 使用自定义图片

如果需要使用其他图片：

```javascript
import customImage from './path/to/your/image.png';

<EmptyState
  text="暂无数据"
  image={customImage}
/>
```

### 不显示图片

```javascript
<EmptyState
  image={null}
  text="仅显示文字的缺省页"
/>
```

## 样式定制

如果需要调整样式，可以通过以下方式：

### 1. 使用 className

```jsx
<EmptyState className="custom-empty-state" />
```

```scss
.custom-empty-state {
  .empty-state-container {
    border-color: #your-color;
  }
  
  .empty-state-text {
    color: #your-text-color;
  }
}
```

### 2. 使用 style 属性

```jsx
<EmptyState 
  style={{ 
    height: '400px',
    backgroundColor: '#f0f8ff' 
  }}
/>
```

## 注意事项

1. **高度设置**：组件默认占满整个视口高度，请根据实际需要调整
2. **响应式**：组件已内置移动端适配，无需额外处理
3. **图片优化**：建议使用 @2x 或 @3x 的高清图片以适配不同屏幕密度
4. **性能**：组件轻量级，可以放心在多个页面中使用
