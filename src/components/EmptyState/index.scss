.empty-state-root {
  width: 100%;
  height: 100vh;
  background-color: #f5f5dc; /* 浅黄色背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.empty-state-container {
  width: 100%;
  max-width: 400px;
  height: 60%;
  min-height: 300px;
  border: 2px dashed #d4af37; /* 金黄色虚线边框 */
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1); /* 半透明白色背景 */
}

.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.empty-state-image {
  margin-bottom: 20px;
  
  img {
    width: 120px;
    height: auto;
    opacity: 0.8;
  }
}

.empty-state-text {
  font-size: 16px;
  color: #8b7355; /* 深棕色文字 */
  font-weight: 500;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .empty-state-container {
    height: 50%;
    min-height: 250px;
  }
  
  .empty-state-image img {
    width: 100px;
  }
  
  .empty-state-text {
    font-size: 14px;
  }
}

/* 小尺寸变体 */
.empty-state-root.small {
  height: auto;
  min-height: 200px;
  
  .empty-state-container {
    height: auto;
    min-height: 150px;
    padding: 20px;
  }
  
  .empty-state-image {
    margin-bottom: 12px;
    
    img {
      width: 80px;
    }
  }
  
  .empty-state-text {
    font-size: 12px;
  }
}
