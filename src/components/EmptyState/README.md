# EmptyState 缺省页组件

一个全项目通用的缺省页组件，用于显示"暂无数据"状态。

## 特性

- 🎨 **统一设计**：浅黄色背景配合虚线边框的设计风格
- 📱 **响应式**：支持移动端和桌面端的自适应显示
- 🔧 **可配置**：支持自定义文本、图片和样式
- 📦 **轻量级**：简单易用，无额外依赖

## 基本用法

```jsx
import { EmptyState } from 'src/components/EmptyState';

// 基本使用（自动显示默认图片）
<EmptyState />

// 自定义文本
<EmptyState text="暂无相关数据" />

// 不显示图片
<EmptyState image={null} text="仅显示文字" />

// 使用自定义图片
<EmptyState
  text="暂无数据"
  image={customImage}
/>
```

## API

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| className | 自定义样式类名 | string | - |
| style | 自定义样式对象 | React.CSSProperties | {} |
| text | 显示的文本内容 | string | '暂无数据' |
| image | 图片路径，设置为 null 可不显示图片 | string \| null | 默认图片 |
| children | 自定义内容，会覆盖 text 参数 | React.ReactNode | - |

## 样式变体

### 小尺寸模式

```jsx
<EmptyState className="small" text="暂无数据" />
```

### 自定义样式

```jsx
<EmptyState 
  className="custom-empty-state"
  style={{ height: '300px' }}
  text="自定义缺省页"
/>
```

## 使用场景

- 列表数据为空时的占位显示
- 搜索结果为空的提示页面
- 网络错误或加载失败的友好提示
- 功能模块暂未开放的说明页面

## 注意事项

1. **图片资源**：请将图片文件放置在 `src/components/EmptyState/images/` 目录下
2. **高度设置**：默认组件会占满整个视口高度，如需调整请使用 style 属性
3. **响应式**：组件已内置响应式设计，在小屏设备上会自动调整尺寸

## 样式定制

如需定制样式，可以通过以下 CSS 类名进行覆盖：

```scss
.empty-state-root {
  // 根容器样式
}

.empty-state-container {
  // 虚线框容器样式
}

.empty-state-content {
  // 内容区域样式
}

.empty-state-image {
  // 图片区域样式
}

.empty-state-text {
  // 文本区域样式
}
```
