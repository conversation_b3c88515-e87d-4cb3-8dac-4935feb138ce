<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmptyState 组件测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
        .test-container {
            border: 1px solid #ddd;
            margin-bottom: 20px;
            height: 300px;
            position: relative;
        }
        .test-title {
            background: #f5f5f5;
            padding: 10px;
            margin: 0;
            border-bottom: 1px solid #ddd;
        }
        .test-content {
            height: calc(100% - 41px);
            position: relative;
        }
        
        /* 模拟组件样式 */
        .empty-state-root {
            width: 100%;
            height: 100%;
            background-color: #f5f5dc;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .empty-state-container {
            width: 100%;
            max-width: 400px;
            height: 60%;
            min-height: 200px;
            border: 2px dashed #d4af37;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .empty-state-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .empty-state-image {
            margin-bottom: 20px;
        }
        
        .empty-state-image img {
            width: 120px;
            height: auto;
            opacity: 0.8;
        }
        
        .empty-state-text {
            font-size: 16px;
            color: #8b7355;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .small .empty-state-container {
            height: auto;
            min-height: 150px;
            padding: 20px;
        }
        
        .small .empty-state-image {
            margin-bottom: 12px;
        }
        
        .small .empty-state-image img {
            width: 80px;
        }
        
        .small .empty-state-text {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>EmptyState 组件样式预览</h1>
    
    <div class="test-container">
        <h3 class="test-title">1. 基本使用（带默认图片）</h3>
        <div class="test-content">
            <div class="empty-state-root">
                <div class="empty-state-container">
                    <div class="empty-state-content">
                        <div class="empty-state-image">
                            <img src="./images/empty_icon.png" alt="暂无数据" />
                        </div>
                        <div class="empty-state-text">暂无数据</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h3 class="test-title">2. 自定义文本</h3>
        <div class="test-content">
            <div class="empty-state-root">
                <div class="empty-state-container">
                    <div class="empty-state-content">
                        <div class="empty-state-image">
                            <img src="./images/empty_icon.png" alt="暂无数据" />
                        </div>
                        <div class="empty-state-text">暂无相关数据，请稍后再试</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h3 class="test-title">3. 小尺寸模式</h3>
        <div class="test-content">
            <div class="empty-state-root small">
                <div class="empty-state-container">
                    <div class="empty-state-content">
                        <div class="empty-state-image">
                            <img src="./images/empty_icon.png" alt="暂无数据" />
                        </div>
                        <div class="empty-state-text">暂无数据</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h3 class="test-title">4. 仅显示文字（无图片）</h3>
        <div class="test-content">
            <div class="empty-state-root">
                <div class="empty-state-container">
                    <div class="empty-state-content">
                        <div class="empty-state-text">仅显示文字的缺省页</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
