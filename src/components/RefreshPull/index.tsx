import React, { useRef, useEffect, useState, useCallback } from 'react';
import classNames from 'classnames';
import { REFRESH_STATE, LOAD_STATE } from '../../constants';
import './index.scss';

interface RefreshConfig {
  state: number;
  handler: () => void;
}

interface LoadConfig {
  state: number;
  handler: () => void;
  distance?: number;
}

interface RefreshPullProps {
  children: React.ReactNode;
  className?: string;
  refresh?: RefreshConfig;
  load?: LoadConfig;
}

const PULL_THRESHOLD = 60; // 下拉触发阈值
const PULL_MAX_DISTANCE = 120; // 最大下拉距离

export const RefreshPull: React.FC<RefreshPullProps> = ({
  children,
  className,
  refresh,
  load,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  const [startY, setStartY] = useState(0);
  const [scrollTop, setScrollTop] = useState(0);
  const [internalPullState, setInternalPullState] = useState(
    REFRESH_STATE.normal,
  );

  // 获取刷新状态文本
  const getRefreshText = useCallback(() => {
    if (!refresh) return '';

    // 如果外部状态是loading，显示加载中
    if (refresh.state === REFRESH_STATE.loading) {
      return '加载中...';
    }

    // 否则根据内部下拉状态显示文本
    switch (internalPullState) {
      case REFRESH_STATE.pull:
        return '下拉刷新';
      case REFRESH_STATE.drop:
        return '释放立即刷新';
      case REFRESH_STATE.loading:
        return '加载中...';
      case REFRESH_STATE.success:
        return '加载成功';
      case REFRESH_STATE.failure:
        return '加载失败';
      default:
        return '';
    }
  }, [refresh, internalPullState]);

  // 获取加载状态文本
  const getLoadText = useCallback(() => {
    if (!load) return '';

    switch (load.state) {
      case LOAD_STATE.loading:
        return '加载中...';
      case LOAD_STATE.success:
        return '加载成功';
      case LOAD_STATE.failure:
        return '加载失败';
      case LOAD_STATE.complete:
        return '我是有底线的';
      default:
        return '';
    }
  }, [load]);

  // 处理触摸开始
  const handleTouchStart = useCallback(
    (e: TouchEvent) => {
      if (!refresh || refresh.state === REFRESH_STATE.loading) return;

      const container = containerRef.current;
      if (!container) return;

      const touch = e.touches[0];
      setStartY(touch.clientY);
      setScrollTop(container.scrollTop);
    },
    [refresh],
  );

  // 处理触摸移动
  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!refresh || refresh.state === REFRESH_STATE.loading) return;

      const container = containerRef.current;
      if (!container) return;

      const touch = e.touches[0];
      const deltaY = touch.clientY - startY;

      // 只有在顶部且向下拉时才触发下拉刷新
      if (container.scrollTop === 0 && deltaY > 0) {
        e.preventDefault();
        const distance = Math.min(deltaY * 0.5, PULL_MAX_DISTANCE);
        setPullDistance(distance);
        setIsPulling(true);

        // 更新内部下拉状态
        if (distance >= PULL_THRESHOLD) {
          setInternalPullState(REFRESH_STATE.drop);
        } else {
          setInternalPullState(REFRESH_STATE.pull);
        }
      } else if (deltaY < 0 || container.scrollTop > 0) {
        // 向上滑动或不在顶部时重置状态
        setPullDistance(0);
        setIsPulling(false);
        setInternalPullState(REFRESH_STATE.normal);
      }
    },
    [refresh, startY],
  );

  // 处理触摸结束
  const handleTouchEnd = useCallback(() => {
    if (!refresh || refresh.state === REFRESH_STATE.loading) return;

    if (pullDistance >= PULL_THRESHOLD) {
      // 触发刷新
      refresh.handler();
    }

    setPullDistance(0);
    setIsPulling(false);
    setInternalPullState(REFRESH_STATE.normal);
  }, [refresh, pullDistance]);

  // 处理滚动事件，检查是否需要加载更多
  const handleScroll = useCallback(() => {
    if (
      !load ||
      load.state === LOAD_STATE.loading ||
      load.state === LOAD_STATE.complete
    )
      return;

    const container = containerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const distance = load.distance || 50;

    // 当滚动到接近底部时触发加载
    if (scrollHeight - scrollTop - clientHeight <= distance) {
      load.handler();
    }
  }, [load]);

  // 绑定事件监听器
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 触摸事件绑定在滚动容器上
    container.addEventListener('touchstart', handleTouchStart, {
      passive: false,
    });
    container.addEventListener('touchmove', handleTouchMove, {
      passive: false,
    });
    container.addEventListener('touchend', handleTouchEnd);

    // 滚动事件也绑定在滚动容器上
    container.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
      container.removeEventListener('scroll', handleScroll);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, handleScroll]);

  return (
    <div className={classNames('refresh-pull', className)}>
      {/* 下拉刷新指示器 */}
      {refresh && (
        <div
          className={classNames('refresh-pull__refresh', {
            'refresh-pull__refresh--show':
              pullDistance > 0 || refresh.state === REFRESH_STATE.loading,
            'refresh-pull__refresh--loading':
              refresh.state === REFRESH_STATE.loading,
          })}
          style={{
            height:
              refresh.state === REFRESH_STATE.loading
                ? '60px'
                : `${pullDistance}px`,
            marginTop:
              refresh.state === REFRESH_STATE.loading
                ? '0'
                : `-${pullDistance}px`,
          }}
        >
          <div className="refresh-pull__refresh-content">
            {(refresh.state === REFRESH_STATE.loading ||
              internalPullState === REFRESH_STATE.loading) && (
              <div className="refresh-pull__loading-icon" />
            )}
            <span className="refresh-pull__refresh-text">
              {getRefreshText()}
            </span>
          </div>
        </div>
      )}

      {/* 滚动容器 */}
      <div
        ref={containerRef}
        className="refresh-pull__scroll-container"
        style={{
          transform:
            pullDistance > 0 ? `translateY(${pullDistance}px)` : 'none',
          transition: isPulling ? 'none' : 'transform 0.3s ease',
        }}
      >
        {/* 内容区域 */}
        <div ref={contentRef} className="refresh-pull__content">
          <div className="refresh-pull__body">{children}</div>

          {/* 上拉加载指示器 */}
          {load && load.state !== LOAD_STATE.normal && (
            <div
              className={classNames('refresh-pull__load', {
                'refresh-pull__load--show': load.state !== LOAD_STATE.normal,
              })}
            >
              <div className="refresh-pull__load-content">
                {load.state === LOAD_STATE.loading && (
                  <div className="refresh-pull__loading-icon" />
                )}
                <span className="refresh-pull__load-text">{getLoadText()}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RefreshPull;
