.refresh-pull {
  position: relative;
  height: 100%;
  overflow: hidden;

  &__refresh {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f8f8;
    overflow: hidden;
    transition: height 0.3s ease;

    &--show {
      display: flex;
    }

    &--loading {
      height: 60px !important;
      margin-top: 0 !important;
    }

    &-content {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 10px;
    }

    &-text {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }
  }

  &__scroll-container {
    position: relative;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    z-index: 1;
  }

  &__content {
    min-height: 100%;
    background-color: white;
    position: relative;
  }

  &__body {
    min-height: 100%;
  }

  &__load {
    display: none;
    align-items: center;
    justify-content: center;
    padding: 15px;
    background-color: #f8f8f8;
    border-top: 1px solid #e5e5e5;

    &--show {
      display: flex;
    }

    &-content {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }

    &-text {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }
  }

  &__loading-icon {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e5e5;
    border-top: 2px solid #007aff;
    border-radius: 50%;
    animation: refresh-pull-spin 1s linear infinite;
  }
}

@keyframes refresh-pull-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 兼容原有的kymui-pull样式类名
.kymui-pull {
  @extend .refresh-pull;

  .kymui-pull__content {
    @extend .refresh-pull__content;

    // 保持原有的背景图片样式
    padding-top: 1px;
    background: url(../../containers/newHome/NewManageBoard/images/<EMAIL>)
      no-repeat top;
    background-color: white;
    background-size: 100% auto;
  }

  .kymui-pull__body {
    @extend .refresh-pull__body;
  }

  .kymui-pull__refresh {
    @extend .refresh-pull__refresh;
  }

  .kymui-pull__load {
    @extend .refresh-pull__load;

    &--show {
      @extend .refresh-pull__load--show;
    }
  }
}

// 为了保持与现有样式的兼容性，添加一些特定的样式覆盖
.refresh-pull {
  // 继承NewManageBoard中的特定样式
  &.new-manage-board-pull {
    .refresh-pull__content {
      min-height: 100%;
      padding-top: 1px;
      background: url(../../containers/newHome/NewManageBoard/images/<EMAIL>)
        no-repeat top;
      background-color: white;
      background-size: 100% auto;
    }
  }

  // 继承PullList中的特定样式
  &.pull-list-style {
    overflow-x: visible;
    overflow-y: auto;
    height: 100%;
    padding: 0 10px;
  }

  // 继承CargoManage中的特定样式
  &.cargo-manage-style {
    .refresh-pull__refresh,
    .refresh-pull__load--show {
      height: fit-content;
      min-height: var(--pull-control-height, 50px);
    }
  }
}
