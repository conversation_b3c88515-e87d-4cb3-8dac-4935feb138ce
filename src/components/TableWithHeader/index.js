import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import { RefreshPull } from 'src/components/RefreshPull';
import { NoData } from 'src/components/NoData';
import './index.scss';

const usePullState = (
  initState = {
    refreshing: 0,
    filter: '',
    status: 0,
    currentPage: 1,
    datas: [],
    loading: 0,
  },
) => {
  const {
    refreshing: initRefreshing,
    loading: initLoading,
    filter: initFilter,
    status: initStatus,
    currentPage: initCurrentPage,
  } = initState;
  const [refreshing, setRefreshing] = useState(initRefreshing);
  const [datas, setDatas] = useState([]);
  const [loading, setLoading] = useState(initLoading);
  const [filter, setFilter] = useState(initFilter);
  const [status, setStatus] = useState(initStatus);
  const [currentPage, setCurrentPage] = useState(initCurrentPage);
  return {
    refreshing,
    setRefreshing,
    datas,
    setDatas,
    filter,
    setFilter,
    status,
    setStatus,
    currentPage,
    setCurrentPage,
    loading,
    setLoading,
  };
};

// 表格行组件
const TableRow = ({ data, columnWidths, cellFontSizes, totalWidth }) => {
  if (!data || !Array.isArray(data)) {
    return null;
  }

  return (
    <div className="table-with-header-row">
      <div className="table-row-content" style={{ minWidth: totalWidth }}>
        {data.map((cell, cellIndex) => (
          <div
            key={`cell-${cellIndex}`}
            className="table-cell"
            style={{
              width:
                columnWidths && columnWidths[cellIndex]
                  ? columnWidths[cellIndex]
                  : 80,
              minWidth:
                columnWidths && columnWidths[cellIndex]
                  ? columnWidths[cellIndex]
                  : 80,
              fontSize:
                cellFontSizes && cellFontSizes[cellIndex]
                  ? cellFontSizes[cellIndex]
                  : '12px', // 默认字体大小
            }}
          >
            {cell || '-'}
          </div>
        ))}
      </div>
    </div>
  );
};

// 主组件
export default function TableWithHeader(props) {
  const {
    headers = [], // 表头数组
    dataSource: initDataSource = [], // 初始数据源
    columnWidths = [], // 列宽度配置
    headerFontSizes = [], // 表头字体大小配置
    cellFontSizes = [], // 单元格字体大小配置
    loading: initLoading,
    currentPage: initCurrentPage = 0,
    filter: initFilter = '',
    status: initStatus = 0,
    load = () => {},
    refresh = () => {},
    refreshing: initRefreshing = 0,
    className = '',
    style = {},
    headerStyle = {},
    bodyStyle = {},
    ...others
  } = props;

  // 滚动容器引用
  const headerScrollRef = useRef(null);
  const bodyScrollRef = useRef(null);
  const bottomScrollbarRef = useRef(null);
  const scrollThumbRef = useRef(null);

  // 状态管理
  const [containerHeight, setContainerHeight] = useState(0);
  const [isFirst, updateFirst] = useState(true);
  const [scrollThumbWidth, setScrollThumbWidth] = useState(0);
  const [scrollThumbLeft, setScrollThumbLeft] = useState(0);
  const [showBottomScrollbar, setShowBottomScrollbar] = useState(false);
  const {
    refreshing,
    setRefreshing,
    datas,
    setDatas,
    currentPage,
    setCurrentPage,
    loading,
    setLoading,
  } = usePullState({
    datas: initDataSource,
    currentPage: initCurrentPage,
    loading: initLoading,
    filter: initFilter,
    status: initStatus,
    refreshing: initRefreshing,
  });

  // 计算总宽度
  const totalWidth = useMemo(() => {
    if (columnWidths && columnWidths.length > 0) {
      return columnWidths.reduce((sum, width) => sum + width, 0);
    }
    return headers.length * 80; // 默认每列80px
  }, [columnWidths, headers]);

  // 滚动同步处理
  const syncScrollLeft = useCallback((sourceElement, targetElement) => {
    if (sourceElement && targetElement) {
      targetElement.scrollLeft = sourceElement.scrollLeft;
    }
  }, []);

  // 表头滚动处理
  const handleHeaderScroll = useCallback(
    e => {
      const headerElement = e.target;
      // 查找表体的滚动容器
      const bodyScrollElement = bodyScrollRef.current?.querySelector(
        '.table-body-scroll',
      );
      if (bodyScrollElement) {
        syncScrollLeft(headerElement, bodyScrollElement);
      }
    },
    [syncScrollLeft],
  );

  // 表体滚动处理
  const handleBodyScroll = useCallback(
    e => {
      const bodyElement = e.target;
      const headerElement = headerScrollRef.current;
      // 确保只在表体滚动时同步
      if (bodyElement.classList.contains('table-body-scroll')) {
        syncScrollLeft(bodyElement, headerElement);
        updateBottomScrollbar(bodyElement);
      }
    },
    [syncScrollLeft, updateBottomScrollbar],
  );

  // 更新底部滚动条位置
  const updateBottomScrollbar = useCallback(scrollElement => {
    if (!scrollElement) return;

    const scrollWidth = scrollElement.scrollWidth;
    const clientWidth = scrollElement.clientWidth;
    const scrollLeft = scrollElement.scrollLeft;

    // 判断是否需要显示滚动条
    const needsScrollbar = scrollWidth > clientWidth;
    setShowBottomScrollbar(needsScrollbar);

    if (needsScrollbar) {
      // 计算滚动条拇指的宽度和位置（基于30px固定轨道宽度）
      const trackWidth = 30; // 固定轨道宽度30px
      const thumbWidthRatio = clientWidth / scrollWidth; // 可视区域比例
      const thumbWidthPx = Math.max(thumbWidthRatio * trackWidth, 6); // 最小6px宽度
      const thumbWidthPercent = (thumbWidthPx / trackWidth) * 100; // 转换为百分比

      const maxScrollLeft = scrollWidth - clientWidth;
      const scrollRatio = maxScrollLeft > 0 ? scrollLeft / maxScrollLeft : 0;
      const maxThumbLeft = trackWidth - thumbWidthPx;
      const thumbLeftPx = scrollRatio * maxThumbLeft;
      const thumbLeftPercent = (thumbLeftPx / trackWidth) * 100;

      setScrollThumbWidth(thumbWidthPercent);
      setScrollThumbLeft(thumbLeftPercent);
    }
  }, []);

  // 底部滚动条点击处理
  const handleBottomScrollbarClick = useCallback(e => {
    const scrollTrack = e.currentTarget;
    const rect = scrollTrack.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const trackWidth = rect.width;
    const clickPercent = clickX / trackWidth;

    // 找到表体滚动元素
    const bodyScrollElement = bodyScrollRef.current?.querySelector(
      '.table-body-scroll',
    );
    if (bodyScrollElement) {
      const scrollWidth = bodyScrollElement.scrollWidth;
      const clientWidth = bodyScrollElement.clientWidth;
      const maxScrollLeft = scrollWidth - clientWidth;
      const newScrollLeft = clickPercent * maxScrollLeft;

      bodyScrollElement.scrollLeft = newScrollLeft;
      // 同步表头滚动
      if (headerScrollRef.current) {
        headerScrollRef.current.scrollLeft = newScrollLeft;
      }
    }
  }, []);

  // 初始化和数据刷新逻辑
  useEffect(() => {
    if (currentPage === 0) {
      refresh.call(this, {
        setLoading,
        loading,
        setCurrentPage,
        datas,
        currentPage,
        setRefreshing,
        setDatas,
        isStart: true,
      });
    }

    const pageHeight = document.documentElement.offsetHeight;

    // 根据页面路径设置容器高度
    if (
      window.location.hash.slice(1) === '/stock/must-go' ||
      window.location.hash.slice(1) === '/total'
    ) {
      const tableHeaderElement = document.querySelector(
        '.table-with-header .table-header',
      );
      if (tableHeaderElement) {
        setContainerHeight(
          pageHeight - tableHeaderElement.getClientRects()[0].top,
        );
      }
    }
  }, [currentPage]);

  useEffect(() => {
    if (isFirst) {
      updateFirst(false);
      return;
    }
    refresh.call(this, {
      setLoading,
      setCurrentPage,
      datas,
      filter: initFilter,
      status: initStatus,
      setRefreshing,
      setDatas,
      isStart: true,
    });
  }, [initFilter]);

  // 初始化底部滚动条
  useEffect(() => {
    const bodyScrollElement = bodyScrollRef.current?.querySelector(
      '.table-body-scroll',
    );
    if (bodyScrollElement) {
      updateBottomScrollbar(bodyScrollElement);

      // 监听窗口大小变化
      const handleResize = () => {
        updateBottomScrollbar(bodyScrollElement);
      };

      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [datas, updateBottomScrollbar]);

  return (
    <div className={`table-with-header ${className}`} style={style}>
      {/* 表头区域 */}
      <div className="table-header" style={headerStyle}>
        <div
          className="table-header-scroll"
          ref={headerScrollRef}
          onScroll={handleHeaderScroll}
        >
          <div
            className="table-header-content"
            style={{ minWidth: totalWidth }}
          >
            {headers.map((header, headerIndex) => {
              const fontSize =
                headerFontSizes && headerFontSizes[headerIndex]
                  ? headerFontSizes[headerIndex]
                  : '11px';

              // 调试信息
              console.log(
                `Header ${headerIndex}: ${header}, fontSize: ${fontSize}`,
              );

              return (
                <div
                  key={`header-${headerIndex}`}
                  className="table-header-cell"
                  style={{
                    width:
                      columnWidths && columnWidths[headerIndex]
                        ? columnWidths[headerIndex]
                        : 80,
                    minWidth:
                      columnWidths && columnWidths[headerIndex]
                        ? columnWidths[headerIndex]
                        : 80,
                    fontSize: fontSize,
                  }}
                >
                  {header}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* 表体区域 */}
      <div className="table-body" style={bodyStyle}>
        <div
          className="table-body-container"
          ref={bodyScrollRef}
          style={{ height: +containerHeight || 'inherit' }}
        >
          <RefreshPull
            className="table-body-refresh-pull"
            refresh={{
              state: refreshing,
              handler: () => {
                refresh.call(this, {
                  setLoading,
                  setCurrentPage,
                  currentPage,
                  datas,
                  setRefreshing,
                  setDatas,
                });
              },
            }}
            load={{
              state: loading,
              distance: 50,
              handler: () =>
                load.call(this, {
                  setLoading,
                  loading,
                  setCurrentPage,
                  datas,
                  currentPage,
                  setRefreshing,
                  setDatas,
                }),
            }}
          >
            <div className="table-body-scroll" onScroll={handleBodyScroll}>
              {datas && datas.length && datas[0] ? (
                datas.map((data, rowIndex) => (
                  <TableRow
                    key={`row-${rowIndex}-${JSON.stringify(data).slice(0, 20)}`}
                    data={data}
                    columnWidths={columnWidths}
                    cellFontSizes={cellFontSizes}
                    totalWidth={totalWidth}
                    {...others}
                  />
                ))
              ) : (
                <div className="table-empty">
                  <NoData small tooltip="暂无数据" />
                </div>
              )}
            </div>
          </RefreshPull>
        </div>
      </div>

      {/* 底部水平滚动条 */}
      {showBottomScrollbar && (
        <div className="table-bottom-scrollbar" ref={bottomScrollbarRef}>
          <div className="scrollbar-track" onClick={handleBottomScrollbarClick}>
            <div
              className="scrollbar-thumb"
              ref={scrollThumbRef}
              style={{
                width: `${scrollThumbWidth}%`,
                left: `${scrollThumbLeft}%`,
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
