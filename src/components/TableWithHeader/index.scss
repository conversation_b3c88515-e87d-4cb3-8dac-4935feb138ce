.table-with-header {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  position: relative; /* 为滚动条定位提供参考 */

  .table-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #ffffff;
    // border-bottom: 0.5px solid #f2f2f2;
    flex-shrink: 0;

    .table-header-scroll {
      overflow-x: auto;
      overflow-y: hidden;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
      background: rgba(240, 243, 249, 1);

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
        height: 0;
        width: 0;
      }

      .table-header-content {
        display: flex;
        align-items: center;
        height: 35px;
        line-height: 35px;
        padding-left: 15px;
        font-size: 10px;
        padding-right: 15px;
        background: rgba(240, 243, 249, 1); /* 确保内容区域背景为白色 */

        .table-header-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC-Regular, sans-serif;
          // font-size: 11px; // 移除固定字体大小，使用props传递的字体大小
          color: #888888;
          white-space: nowrap;
          flex-shrink: 0;
          // border-right: 0.5px solid #f2f2f2;
          // background: #ffffff; /* 确保每个单元格背景为白色 */
          padding: 0 2px; /* 添加内边距防止文字重叠 */

          &:last-child {
            border-right: none;
          }
        }
      }
    }
  }

  .table-body {
    flex: 1;
    // background: #f2f2f2;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    position: relative; /* 为滚动条定位提供参考 */

    .table-body-container {
      height: 100%;
      overflow: hidden;
      // background: #f2f2f2; /* 确保容器背景色 */

      .kymui-pull {
        height: 100%;
        overflow: auto;
        -webkit-overflow-scrolling: touch;
        // background: #f2f2f2; /* 确保 Pull 组件背景色 */

        .kymui-pull__content {
          min-height: 100%;
          display: flex;
          flex-direction: column;
          // background: #f2f2f2; /* 确保内容区域背景色 */
        }

        .kymui-pull__body {
          flex: 1;
          min-height: 0;
          // background: #f2f2f2; /* 确保主体区域背景色 */
        }
      }

      .table-body-scroll {
        overflow-x: auto;
        overflow-y: visible;
        -webkit-overflow-scrolling: touch;
        background: #ffffff;
        /* 完全隐藏原生滚动条 */
        scrollbar-width: none; /* Firefox - 隐藏滚动条 */
        -ms-overflow-style: none; /* IE and Edge - 隐藏滚动条 */

        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera - 完全隐藏滚动条 */
          height: 0;
          width: 0;
        }

        &::-webkit-scrollbar-track {
          display: none; /* 隐藏滚动条轨道 */
        }

        &::-webkit-scrollbar-thumb {
          display: none; /* 隐藏滚动条拇指 */
        }
      }

      .table-with-header-row {
        background: #fff;
        margin-bottom: 1px;
        min-height: 50px;

        &:last-child {
          margin-bottom: 0;
        }

        .table-row-content {
          display: flex;
          align-items: center;
          height: 50px;
          padding-left: 15px;
          padding-right: 15px;
          background: #ffffff; /* 确保行内容背景为白色 */

          .table-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-family: DINAlternate-Bold, sans-serif;
            // font-size: 12px; // 移除固定字体大小，使用props传递的字体大小
            color: #000000;
            line-height: 15px;
            white-space: nowrap;
            flex-shrink: 0;
            // border-right: 0.5px solid #f2f2f2;
            padding: 0 3px; /* 减少内边距防止重叠 */
            background: #ffffff; /* 确保每个单元格背景为白色 */
            overflow: hidden; /* 防止内容溢出 */
            text-overflow: ellipsis; /* 文字过长时显示省略号 */

            &:last-child {
              border-right: none;
            }
          }
        }
      }

      .table-empty {
        display: flex;
        align-items: center;
        justify-content: center;
        // height: 200px;
        background: #fff;
        color: #999;
        font-size: 14px;
      }
    }
  }

  /* 底部水平滚动条区域 */
  .table-bottom-scrollbar {
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    z-index: 15;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    .scrollbar-track {
      width: 30px; /* 固定宽度30px */
      height: 4px;
      background: rgba(235, 238, 245, 1);
      border-radius: 3px;
      position: relative;
      overflow: hidden;

      .scrollbar-thumb {
        height: 100%;
        background: rgba(220, 30, 50, 1); /* 新的红色背景 */
        border-radius: 3px;
        position: absolute;
        top: 0;
        left: 0;
        transition: background-color 0.2s ease;
        cursor: pointer;

        &:hover {
          background: rgba(200, 25, 45, 1); /* 悬停时的深红色 */
        }

        &:active {
          background: rgba(180, 20, 40, 1); /* 激活时的更深红色 */
        }
      }
    }
  }
}

// 移动端优化
@media (max-width: 768px) {
  .table-with-header {
    .table-body {
      .table-body-container {
        .table-with-header-row {
          .table-row-content {
            .table-cell {
              // font-size: 11px; // 移除固定字体大小，使用props传递的字体大小
              padding: 0 3px;
            }
          }
        }
      }
    }
  }
}

// 全屏模式适配
.full-screen {
  .table-with-header {
    .table-header {
      top: calc(48px + var(--sat));
    }
  }
}

.iphone-fit {
  .table-with-header {
    .table-header {
      top: 68px;
    }
  }
}

// 特定页面样式覆盖 - 仅保留必要的页面特定样式
.three-days-no-weight-detail-wrap {
  .table-with-header {
    .table-header {
      .table-header-content {
        .table-header-cell {
          &:nth-child(1) {
            // 员工编码
            width: 70px;
            min-width: 70px;
          }
          &:nth-child(2) {
            // 员工姓名
            width: 70px;
            min-width: 70px;
          }
          &:nth-child(3) {
            // 岗位
            width: 50px;
            min-width: 50px;
          }
          &:nth-child(4) {
            // 当日总货量
            width: 80px;
            min-width: 80px;
            font-size: 10px; // 减小字体防止重叠
          }
          &:nth-child(5) {
            // 当月总货量
            width: 80px;
            min-width: 80px;
            font-size: 10px; // 减小字体防止重叠
          }
          &:nth-child(6) {
            // 当月偷重偷方上报票数
            width: 130px;
            min-width: 130px;
            font-size: 8px; // 更小字体适应长文本
            line-height: 1.1; // 调整行高
            padding: 0 2px; // 减少内边距
          }
          &:nth-child(7) {
            // 当月出勤天数
            width: 80px;
            min-width: 80px;
            font-size: 10px; // 减小字体防止重叠
          }
          &:nth-child(8) {
            // 当月效能
            width: 70px;
            min-width: 70px;
          }
        }
      }
    }

    .table-body {
      .table-body-container {
        .table-with-header-row {
          .table-row-content {
            .table-cell {
              &:nth-child(1) {
                // 员工编码
                width: 70px;
                min-width: 70px;
              }
              &:nth-child(2) {
                // 员工姓名
                width: 70px;
                min-width: 70px;
              }
              &:nth-child(3) {
                // 岗位
                width: 50px;
                min-width: 50px;
              }
              &:nth-child(4) {
                // 当日总货量
                width: 80px;
                min-width: 80px;
              }
              &:nth-child(5) {
                // 当月总货量
                width: 80px;
                min-width: 80px;
              }
              &:nth-child(6) {
                // 当月偷重偷方上报票数
                width: 130px;
                min-width: 130px;
                font-size: 9px;
                line-height: 1.1;
                padding: 0 2px;
              }
              &:nth-child(7) {
                // 当月出勤天数
                width: 80px;
                min-width: 80px;
              }
              &:nth-child(8) {
                // 当月效能
                width: 70px;
                min-width: 70px;
              }
            }
          }
        }
      }
    }
  }
}
