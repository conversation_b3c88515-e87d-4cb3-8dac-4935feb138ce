# TableWithHeader 组件

一个支持横向滚动的表格组件，集成了下拉刷新和上拉加载功能。

## 特性

- 🔄 **表头表体同步滚动**：表头和表体的横向滚动完全同步
- 📱 **移动端优化**：专为移动端设计的触控体验
- 🔃 **下拉刷新**：继承 PullDownList 的下拉刷新功能
- ⬆️ **上拉加载**：支持分页数据的上拉加载
- 🎨 **自适应宽度**：根据内容自动撑开，支持横向滚动
- 🛠️ **简单 API**：通过数组配置表头和数据

## 基本用法

```jsx
import TableWithHeader from 'src/components/TableWithHeader';

function MyComponent() {
  const headers = [
    '时间',
    '员工编码',
    '员工姓名',
    '岗位',
    '装卸组别',
    '当日总货量',
    '当月总货量',
    '当月偷重偷方上报票数',
    '当月出勤天数',
    '当月效能',
  ];

  const dataSource = [
    [
      '2024-06-21 10:30',
      'E001',
      '张三',
      '装卸工',
      'A组',
      '25.5',
      '450.2',
      '2',
      '28',
      '85.3',
    ],
    [
      '2024-06-21 11:15',
      'E002',
      '李四',
      '装卸工',
      'B组',
      '18.2',
      '380.1',
      '1',
      '26',
      '78.9',
    ],
    // ... 更多数据
  ];

  const handleRefresh = ({ setRefreshing, setDatas, setCurrentPage }) => {
    // 下拉刷新逻辑
    fetchData(1).then(newData => {
      setDatas(newData);
      setCurrentPage(1);
      setRefreshing(0);
    });
  };

  const handleLoad = ({
    setLoading,
    setDatas,
    datas,
    currentPage,
    setCurrentPage,
  }) => {
    // 上拉加载逻辑
    fetchData(currentPage + 1).then(newData => {
      setDatas([...datas, ...newData]);
      setCurrentPage(currentPage + 1);
      setLoading(0);
    });
  };

  return (
    <TableWithHeader
      headers={headers}
      dataSource={dataSource}
      refresh={handleRefresh}
      load={handleLoad}
    />
  );
}
```

## API

### Props

| 参数         | 说明             | 类型       | 默认值     |
| ------------ | ---------------- | ---------- | ---------- |
| headers      | 表头数组         | `string[]` | `[]`       |
| dataSource   | 数据源数组       | `any[][]`  | `[]`       |
| columnWidths | 列宽度配置(px)   | `number[]` | `[]`       |
| refresh      | 下拉刷新回调函数 | `function` | `() => {}` |
| load         | 上拉加载回调函数 | `function` | `() => {}` |
| refreshing   | 刷新状态         | `number`   | `0`        |
| loading      | 加载状态         | `number`   | `0`        |
| currentPage  | 当前页码         | `number`   | `0`        |
| className    | 自定义样式类名   | `string`   | `''`       |
| style        | 自定义样式       | `object`   | `{}`       |
| headerStyle  | 表头自定义样式   | `object`   | `{}`       |
| bodyStyle    | 表体自定义样式   | `object`   | `{}`       |

### 回调函数参数

#### refresh 函数参数

```javascript
{
  setRefreshing, // 设置刷新状态
    setDatas, // 设置数据
    setCurrentPage, // 设置当前页码
    datas, // 当前数据
    currentPage, // 当前页码
    setLoading; // 设置加载状态
}
```

#### load 函数参数

```javascript
{
  setLoading, // 设置加载状态
    setDatas, // 设置数据
    setCurrentPage, // 设置当前页码
    datas, // 当前数据
    currentPage, // 当前页码
    setRefreshing; // 设置刷新状态
}
```

## 高级用法

### 自定义列宽

```jsx
<TableWithHeader
  headers={headers}
  dataSource={dataSource}
  columnWidths={[90, 70, 70, 50, 70, 80, 80, 110, 80, 70]}
  refresh={handleRefresh}
  load={handleLoad}
/>
```

### 自定义样式

```jsx
<TableWithHeader
  headers={headers}
  dataSource={dataSource}
  className="my-table"
  style={{ height: '400px' }}
  headerStyle={{ backgroundColor: '#f5f5f5' }}
  bodyStyle={{ backgroundColor: '#fafafa' }}
  refresh={handleRefresh}
  load={handleLoad}
/>
```

## 注意事项

1. **数据格式**：dataSource 必须是二维数组，每个子数组代表一行数据
2. **列宽配置**：columnWidths 数组长度应与 headers 数组长度一致
3. **滚动同步**：表头和表体的滚动会自动同步，无需手动处理
4. **性能优化**：大量数据时建议使用分页加载，避免一次性渲染过多行

## 样式定制

组件提供了丰富的 CSS 类名供样式定制：

```scss
.table-with-header {
  .table-header {
    .table-header-cell {
      // 自定义表头单元格样式
    }
  }

  .table-body {
    .table-cell {
      // 自定义表体单元格样式
    }
  }
}
```

## 兼容性

- 支持 iOS Safari 和 Android Chrome
- 兼容现有的 PullDownList 组件逻辑
- 遵循项目的移动端适配方案
