# TableWithHeader 组件设计原则

## 问题背景

您提出了一个非常重要的问题：**为什么通用组件还需要加特定页面样式？**

这确实是一个设计上的问题。理想情况下，通用组件应该保持独立性，不应该包含特定页面的样式。

## 原有问题

### ❌ 错误的做法
```scss
// 在 TableWithHeader/index.scss 中
.personnel-efficiency-detail-wrap {
  .table-with-header {
    // 特定页面样式...
  }
}

.three-days-no-weight-detail-wrap {
  .table-with-header {
    // 另一个页面的特定样式...
  }
}
```

**问题：**
1. **违反组件设计原则**：通用组件依赖特定页面
2. **维护困难**：页面增多时，组件样式文件会变得臃肿
3. **耦合度高**：组件与页面紧密耦合，不利于复用
4. **样式冲突风险**：不同页面的样式可能相互影响

## 解决方案

### ✅ 正确的做法

#### 1. 配置化设计
通过 props 传递样式配置，保持组件的通用性：

```jsx
<TableWithHeader
  headers={headers}
  columnWidths={[70, 80, 80, 120, 80, 80]}
  headerFontSizes={['11px', '10px', '9px', '8px', '10px', '10px']}
  cellFontSizes={['12px', '12px', '12px', '10px', '12px', '12px']}
/>
```

#### 2. 配置文件管理
创建 `tableConfigs.js` 统一管理不同页面的配置：

```javascript
// tableConfigs.js
export const personnelEfficiencyDetailConfig = {
  day: {
    headers: ['日期', '货量(吨)', '日操作货量', '自有操作员出勤劳效', '日移动货量', '移动出勤率'],
    columnWidths: [70, 80, 80, 120, 80, 80],
    headerFontSizes: ['11px', '10px', '9px', '8px', '10px', '10px'],
    cellFontSizes: ['12px', '12px', '12px', '10px', '12px', '12px'],
  },
  month: {
    headers: ['日期', '货量(吨)', '月操作货量', '自有操作员出勤劳效', '月移动货量'],
    columnWidths: [70, 80, 80, 120, 80],
    headerFontSizes: ['11px', '10px', '9px', '8px', '10px'],
    cellFontSizes: ['12px', '12px', '12px', '10px', '12px'],
  },
};

export const getTableConfig = (pageType, subType = null) => {
  // 返回对应的配置
};
```

#### 3. 页面中使用配置
```jsx
// 在页面组件中
import { getTableConfig } from '../../components/TableWithHeader/tableConfigs';

const tableConfig = getTableConfig('personnelEfficiencyDetail', isDay ? 'day' : 'month');
const { headers, columnWidths, headerFontSizes, cellFontSizes } = tableConfig;

<TableWithHeader
  headers={headers}
  columnWidths={columnWidths}
  headerFontSizes={headerFontSizes}
  cellFontSizes={cellFontSizes}
  // ... 其他props
/>
```

## 设计原则

### 1. 单一职责原则
- **组件职责**：提供表格的基础功能（滚动、刷新、加载）
- **配置职责**：由外部配置决定样式和布局
- **页面职责**：提供具体的配置和数据

### 2. 开闭原则
- **对扩展开放**：可以通过配置支持新的页面需求
- **对修改封闭**：不需要修改组件内部代码

### 3. 依赖倒置原则
- **组件不依赖具体页面**：通过抽象的配置接口工作
- **页面依赖组件接口**：通过标准的 props 使用组件

## 重构成果

经过重构后：

1. **✅ 组件纯净**：`TableWithHeader` 不再包含任何页面特定样式
2. **✅ 配置统一**：所有表格配置都在 `tableConfigs.js` 中管理
3. **✅ 易于维护**：新增页面只需添加配置，不需要修改组件
4. **✅ 高度复用**：组件可以在任何地方使用，只需提供对应配置

## 最佳实践

1. **新增页面时**：
   - 在 `tableConfigs.js` 中添加新的配置对象
   - 使用 `getTableConfig()` 获取配置
   - 不在组件样式文件中添加页面特定样式

2. **修改样式时**：
   - 修改配置文件中的对应配置
   - 不直接修改组件样式

3. **组件开发时**：
   - 保持组件的通用性和独立性
   - 通过 props 接收所有可变的配置
   - 避免硬编码任何页面特定的逻辑

这样的设计让组件真正成为了一个可复用的通用组件，符合良好的软件设计原则。
