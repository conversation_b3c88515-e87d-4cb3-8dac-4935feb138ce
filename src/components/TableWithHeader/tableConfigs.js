// 表格配置文件 - 统一管理不同页面的表格样式配置

// 效能趋势详情页面配置
export const personnelEfficiencyDetailConfig = {
  day: {
    headers: [
      '日期',
      '货量(吨)',
      '日操作货量',
      '自有操作员出勤劳效',
      '日移动货量',
      '移动出勤率',
    ],
    columnWidths: [70, 80, 80, 120, 80, 80],
    headerFontSizes: ['12px', '12px', '12px', '12px', '12px', '12px'],
    cellFontSizes: ['12px', '12px', '12px', '12px', '12px', '12px'],
    minWidth: 510, // 总最小宽度
  },
  month: {
    headers: [
      '日期',
      '货量(吨)',
      '月操作货量',
      '自有操作员出勤劳效',
      '月移动货量',
    ],
    columnWidths: [70, 80, 80, 120, 80],
    headerFontSizes: ['12px', '12px', '12px', '12px', '12px'],
    cellFontSizes: ['12px', '12px', '12px', '12px', '12px'],
    minWidth: 430, // 总最小宽度
  },
};

// 3天无货量详情页面配置
export const threeDaysNoWeightDetailConfig = {
  headers: [
    '员工编码',
    '员工姓名',
    '岗位',
    '当日总货量',
    '当月总货量',
    '当月偷重偷方上报票数',
    '当月出勤天数',
    '当月效能',
  ],
  columnWidths: [70, 70, 50, 80, 80, 130, 80, 70],
  headerFontSizes: [
    '12px',
    '12px',
    '12px',
    '12px',
    '12px',
    '12px',
    '12px',
    '12px',
  ],
  cellFontSizes: [
    '12px',
    '12px',
    '12px',
    '12px',
    '12px',
    '12px',
    '12px',
    '12px',
  ],
  minWidth: 630, // 总最小宽度
};

// 获取配置的工具函数
export const getTableConfig = (pageType, subType = null) => {
  switch (pageType) {
    case 'personnelEfficiencyDetail':
      return subType
        ? personnelEfficiencyDetailConfig[subType]
        : personnelEfficiencyDetailConfig;
    case 'threeDaysNoWeightDetail':
      return threeDaysNoWeightDetailConfig;
    default:
      return {
        headers: [],
        columnWidths: [],
        headerFontSizes: [],
        cellFontSizes: [],
        minWidth: 300,
      };
  }
};

// 默认配置
export const defaultTableConfig = {
  columnWidth: 80,
  headerFontSize: '12px',
  cellFontSize: '12px',
  minWidth: 300,
};
