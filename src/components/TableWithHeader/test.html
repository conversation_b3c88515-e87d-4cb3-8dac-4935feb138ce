<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TableWithHeader 组件测试</title>
    <style>
      body {
        margin: 0;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI',
          'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue',
          Helvetica, Arial, sans-serif;
        background: #f5f5f5;
      }

      .test-container {
        max-width: 400px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .test-header {
        padding: 20px;
        background: #1890ff;
        color: white;
        text-align: center;
      }

      .test-content {
        height: 400px;
        position: relative;
      }

      /* 模拟 TableWithHeader 样式 */
      .table-with-header {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        background: #fff;
        overflow: hidden;
      }

      .table-header {
        position: sticky;
        top: 0;
        z-index: 10;
        background: #ffffff;
        border-bottom: 0.5px solid #f2f2f2;
        flex-shrink: 0;
      }

      .table-header-scroll {
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
      }

      .table-header-scroll::-webkit-scrollbar {
        display: none;
        height: 0;
        width: 0;
      }

      .table-header-content {
        display: flex;
        align-items: center;
        height: 35px;
        padding-left: 15px;
        padding-right: 15px;
        min-width: 800px;
      }

      .table-header-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-size: 11px;
        color: #888888;
        white-space: nowrap;
        flex-shrink: 0;
        border-right: 0.5px solid #f2f2f2;
      }

      .table-body {
        flex: 1;
        background: #f2f2f2;
        overflow: hidden;
      }

      .table-body-scroll {
        overflow-x: auto;
        overflow-y: auto;
        height: 100%;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
      }

      .table-body-scroll::-webkit-scrollbar {
        display: none;
      }

      .table-with-header-row {
        background: #fff;
        margin-bottom: 1px;
        min-height: 50px;
      }

      .table-row-content {
        display: flex;
        align-items: center;
        height: 50px;
        padding-left: 15px;
        padding-right: 15px;
        min-width: 800px;
      }

      .table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-size: 12px;
        color: #000000;
        white-space: nowrap;
        flex-shrink: 0;
        border-right: 0.5px solid #f2f2f2;
        padding: 0 5px;
      }

      /* 列宽配置 */
      .table-header-cell:nth-child(1),
      .table-cell:nth-child(1) {
        width: 90px;
        min-width: 90px;
      }
      .table-header-cell:nth-child(2),
      .table-cell:nth-child(2) {
        width: 70px;
        min-width: 70px;
      }
      .table-header-cell:nth-child(3),
      .table-cell:nth-child(3) {
        width: 70px;
        min-width: 70px;
      }
      .table-header-cell:nth-child(4),
      .table-cell:nth-child(4) {
        width: 50px;
        min-width: 50px;
      }
      .table-header-cell:nth-child(5),
      .table-cell:nth-child(5) {
        width: 70px;
        min-width: 70px;
      }
      .table-header-cell:nth-child(6),
      .table-cell:nth-child(6) {
        width: 80px;
        min-width: 80px;
      }
      .table-header-cell:nth-child(7),
      .table-cell:nth-child(7) {
        width: 80px;
        min-width: 80px;
      }
      .table-header-cell:nth-child(8),
      .table-cell:nth-child(8) {
        width: 110px;
        min-width: 110px;
      }
      .table-header-cell:nth-child(9),
      .table-cell:nth-child(9) {
        width: 80px;
        min-width: 80px;
      }
      .table-header-cell:nth-child(10),
      .table-cell:nth-child(10) {
        width: 70px;
        min-width: 70px;
      }

      .sync-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #52c41a;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <div class="test-header">
        <h2>TableWithHeader 组件测试</h2>
        <p>测试横向滚动同步功能</p>
      </div>

      <div class="test-content">
        <div class="table-with-header">
          <!-- 表头区域 -->
          <div class="table-header">
            <div class="table-header-scroll" id="headerScroll">
              <div class="table-header-content">
                <div class="table-header-cell">时间</div>
                <div class="table-header-cell">员工编码</div>
                <div class="table-header-cell">员工姓名</div>
                <div class="table-header-cell">岗位</div>
                <div class="table-header-cell">装卸组别</div>
                <div class="table-header-cell">当日总货量</div>
                <div class="table-header-cell">当月总货量</div>
                <div class="table-header-cell">当月偷重偷方上报票数</div>
                <div class="table-header-cell">当月出勤天数</div>
                <div class="table-header-cell">当月效能</div>
              </div>
            </div>
          </div>

          <!-- 表体区域 -->
          <div class="table-body">
            <div class="table-body-scroll" id="bodyScroll">
              <!-- 模拟数据行 -->
              <div class="table-with-header-row">
                <div class="table-row-content">
                  <div class="table-cell">06-21 10:30</div>
                  <div class="table-cell">E001</div>
                  <div class="table-cell">张三</div>
                  <div class="table-cell">装卸工</div>
                  <div class="table-cell">A组</div>
                  <div class="table-cell">25.5</div>
                  <div class="table-cell">450.2</div>
                  <div class="table-cell">2</div>
                  <div class="table-cell">28</div>
                  <div class="table-cell">85.3</div>
                </div>
              </div>

              <div class="table-with-header-row">
                <div class="table-row-content">
                  <div class="table-cell">06-21 11:15</div>
                  <div class="table-cell">E002</div>
                  <div class="table-cell">李四</div>
                  <div class="table-cell">装卸工</div>
                  <div class="table-cell">B组</div>
                  <div class="table-cell">18.2</div>
                  <div class="table-cell">380.1</div>
                  <div class="table-cell">1</div>
                  <div class="table-cell">26</div>
                  <div class="table-cell">78.9</div>
                </div>
              </div>

              <div class="table-with-header-row">
                <div class="table-row-content">
                  <div class="table-cell">06-21 14:20</div>
                  <div class="table-cell">E003</div>
                  <div class="table-cell">王五</div>
                  <div class="table-cell">班长</div>
                  <div class="table-cell">A组</div>
                  <div class="table-cell">32.1</div>
                  <div class="table-cell">520.8</div>
                  <div class="table-cell">0</div>
                  <div class="table-cell">30</div>
                  <div class="table-cell">92.5</div>
                </div>
              </div>

              <div class="table-with-header-row">
                <div class="table-row-content">
                  <div class="table-cell">06-21 15:45</div>
                  <div class="table-cell">E004</div>
                  <div class="table-cell">赵六</div>
                  <div class="table-cell">装卸工</div>
                  <div class="table-cell">C组</div>
                  <div class="table-cell">22.8</div>
                  <div class="table-cell">412.6</div>
                  <div class="table-cell">3</div>
                  <div class="table-cell">29</div>
                  <div class="table-cell">81.7</div>
                </div>
              </div>

              <div class="table-with-header-row">
                <div class="table-row-content">
                  <div class="table-cell">06-21 16:30</div>
                  <div class="table-cell">E005</div>
                  <div class="table-cell">孙七</div>
                  <div class="table-cell">装卸工</div>
                  <div class="table-cell">B组</div>
                  <div class="table-cell">19.6</div>
                  <div class="table-cell">365.4</div>
                  <div class="table-cell">1</div>
                  <div class="table-cell">27</div>
                  <div class="table-cell">75.2</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="sync-indicator" id="syncIndicator">
      滚动同步 ✓
    </div>

    <script>
      // 滚动同步逻辑测试
      const headerScroll = document.getElementById('headerScroll');
      const bodyScroll = document.getElementById('bodyScroll');
      const syncIndicator = document.getElementById('syncIndicator');

      let isHeaderScrolling = false;
      let isBodyScrolling = false;

      // 表头滚动时同步表体
      headerScroll.addEventListener('scroll', function() {
        if (isBodyScrolling) return;
        isHeaderScrolling = true;
        bodyScroll.scrollLeft = this.scrollLeft;
        showSyncIndicator();
        setTimeout(() => {
          isHeaderScrolling = false;
        }, 50);
      });

      // 表体滚动时同步表头
      bodyScroll.addEventListener('scroll', function() {
        if (isHeaderScrolling) return;
        isBodyScrolling = true;
        headerScroll.scrollLeft = this.scrollLeft;
        showSyncIndicator();
        setTimeout(() => {
          isBodyScrolling = false;
        }, 50);
      });

      function showSyncIndicator() {
        syncIndicator.style.display = 'block';
        setTimeout(() => {
          syncIndicator.style.display = 'none';
        }, 1000);
      }

      // 页面加载完成提示
      window.addEventListener('load', function() {
        console.log('TableWithHeader 测试页面加载完成');
        console.log('请尝试横向滚动表格来测试同步功能');
      });
    </script>
  </body>
</html>
