import React, { useState } from 'react';
import NewPullDownList from './index';
import { REFRESH_STATE, LOAD_STATE } from '../../constants';

// 示例列表项组件
const ExampleItem = ({ data, index }) => (
  <div style={{ 
    padding: '15px', 
    borderBottom: '1px solid #eee',
    backgroundColor: '#fff',
    margin: '5px 10px',
    borderRadius: '8px'
  }}>
    <h3>项目 {index + 1}</h3>
    <p>{data.title || `示例数据 ${index + 1}`}</p>
    <p style={{ color: '#666', fontSize: '12px' }}>
      {data.description || '这是一个示例描述'}
    </p>
  </div>
);

// 示例使用组件
export const NewPullDownListExample = () => {
  const [refreshing, setRefreshing] = useState(REFRESH_STATE.normal);
  const [loading, setLoading] = useState(LOAD_STATE.normal);
  const [currentPage, setCurrentPage] = useState(1);
  const [datas, setDatas] = useState([]);

  // 模拟数据生成
  const generateMockData = (page = 1, pageSize = 10) => {
    const data = [];
    for (let i = 0; i < pageSize; i++) {
      const index = (page - 1) * pageSize + i;
      data.push({
        id: index,
        title: `标题 ${index + 1}`,
        description: `这是第 ${index + 1} 项的描述信息`,
      });
    }
    return data;
  };

  // 刷新数据
  const handleRefresh = ({ 
    setLoading, 
    setCurrentPage, 
    setDatas, 
    setRefreshing 
  }) => {
    setRefreshing(REFRESH_STATE.loading);
    
    // 模拟网络请求
    setTimeout(() => {
      const newData = generateMockData(1);
      setDatas(newData);
      setCurrentPage(1);
      setRefreshing(REFRESH_STATE.success);
      
      // 显示成功状态一段时间后重置
      setTimeout(() => {
        setRefreshing(REFRESH_STATE.normal);
      }, 1000);
    }, 1500);
  };

  // 加载更多数据
  const handleLoad = ({ 
    setLoading, 
    setCurrentPage, 
    datas, 
    currentPage, 
    setDatas 
  }) => {
    setLoading(LOAD_STATE.loading);
    
    // 模拟网络请求
    setTimeout(() => {
      const nextPage = currentPage + 1;
      const newData = generateMockData(nextPage);
      
      // 模拟最多加载5页
      if (nextPage <= 5) {
        setDatas([...datas, ...newData]);
        setCurrentPage(nextPage);
        setLoading(LOAD_STATE.success);
        
        setTimeout(() => {
          setLoading(LOAD_STATE.normal);
        }, 1000);
      } else {
        // 没有更多数据
        setLoading(LOAD_STATE.complete);
      }
    }, 1500);
  };

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <div style={{ 
        padding: '15px', 
        backgroundColor: '#f8f8f8', 
        borderBottom: '1px solid #eee',
        textAlign: 'center'
      }}>
        <h2>NewPullDownList 示例</h2>
        <p style={{ color: '#666', fontSize: '14px' }}>
          下拉刷新，上拉加载更多
        </p>
      </div>
      
      <div style={{ flex: 1 }}>
        <NewPullDownList
          datas={datas}
          refreshing={refreshing}
          loading={loading}
          currentPage={currentPage}
          load={handleLoad}
          refresh={handleRefresh}
          item={ExampleItem}
          filter=""
          gotoDetail={(item) => {
            console.log('跳转到详情:', item);
          }}
        />
      </div>
    </div>
  );
};

export default NewPullDownListExample;
