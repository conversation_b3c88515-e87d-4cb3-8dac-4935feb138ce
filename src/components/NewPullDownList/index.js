import React, { useState, useEffect } from 'react';
import { LocaleProvider } from 'kymui';
import { RefreshPull } from '../RefreshPull';
import './index.scss';

const usePullState = (
  initState = {
    refreshing: 0,
    filter: '',
    status: 0,
    currentPage: 1,
    datas: [],
    loading: 0,
  },
) => {
  const {
    refreshing: initRefreshing,
    loading: initLoading,
    filter: initFilter,
    status: initStatus,
    currentPage: initCurrentPage,
  } = initState;
  const [refreshing, setRefreshing] = useState(initRefreshing);
  const [datas, setDatas] = useState([]);
  const [loading, setLoading] = useState(initLoading);
  const [filter, setFilter] = useState(initFilter);
  const [status, setStatus] = useState(initStatus);
  const [currentPage, setCurrentPage] = useState(initCurrentPage);
  return {
    refreshing,
    setRefreshing,
    datas,
    setDatas,
    filter,
    setFilter,
    status,
    setStatus,
    currentPage,
    setCurrentPage,
    loading,
    setLoading,
  };
};

const PullLocale = {
  locale: 'zh_cn',
  Pull: {
    pullText: '下拉刷新',
    dropText: '释放立即刷新',
    loadingText: '加载中',
    successText: '加载成功',
    failureText: '加载失败',
    completeText: '我是有底线的',
  },
};

let isOrder = false;
export default function NewPullDownList(props) {
  const {
    datas: initDatas = [],
    loading: initLoading,
    currentPage: initCurrentPage = 0,
    filter: initFilter = '',
    status: initStatus = 0,
    load = () => {},
    refresh = () => {},
    orderData = () => {},
    refreshing: initRefreshing = 0,
    item: Item = null,
    error = null,
    visible = () => {},
    gotoDetail = () => {},
    zoneCode = '',
    order = 0,
    tabValue = 0,
    removeData = () => {},
    ...others
  } = props;
  const [containerHeight, setContainerHeight] = useState(0);
  const [isFirst, updateFirst] = useState(true);
  const {
    refreshing,
    setRefreshing,
    datas,
    setDatas,
    currentPage,
    setStatus,
    setCurrentPage,
    loading,
    setLoading,
  } = usePullState({
    datas: initDatas,
    currentPage: initCurrentPage,
    loading: initLoading,
    filter: initFilter,
    status: setStatus,
    refreshing: initRefreshing,
  });

  useEffect(() => {
    if (currentPage === 0) {
      refresh.call(this, {
        setLoading,
        loading,
        setCurrentPage,
        datas,
        currentPage,
        setRefreshing,
        setDatas,
        isStart: true,
      });
    }

    const pageHeight = document.documentElement.offsetHeight;
    if (
      window.location.hash.slice(1) === '/stock/must-go' ||
      window.location.hash.slice(1) === '/total'
    ) {
      setContainerHeight(
        pageHeight -
          document.querySelector('.table-header').getClientRects()[0].top,
      );
    }
  }, [currentPage]);

  useEffect(() => {
    if (isFirst) {
      updateFirst(false);
      return;
    }
    refresh.call(this, {
      setLoading,
      setCurrentPage,
      datas,
      filter: initFilter,
      status: initStatus,
      setRefreshing,
      setDatas,
      isStart: true,
    });
  }, [initFilter]);

  useEffect(() => {
    if (!isOrder) {
      isOrder = true;
      return;
    }
    orderData.call(this, {
      setDatas,
      datas,
    });
  }, [order]);

  return (
    <LocaleProvider locale={PullLocale}>
      <div
        className="new-pulldown-list"
        style={{ height: +containerHeight || 'inherit' }}
      >
        <RefreshPull
          className="new-pulldown-list-refresh-pull"
          refresh={{
            state: refreshing,
            handler: () => {
              refresh.call(this, {
                setLoading,
                setCurrentPage,
                currentPage,
                datas,
                setRefreshing,
                setDatas,
              });
            },
          }}
          load={{
            state: loading,
            distance: 50,
            handler: () =>
              load.call(this, {
                setLoading,
                loading,
                setCurrentPage,
                datas,
                currentPage,
                setRefreshing,
                setDatas,
              }),
          }}
        >
          {datas && datas.length && datas[0] ? (
            datas.map((data, index) => (
              <Item
                key={JSON.stringify(data)}
                data={data}
                error={error}
                index={index}
                visible={visible}
                zoneCode={zoneCode}
                gotoDetail={gotoDetail}
                tabValue={tabValue}
                removeData={() =>
                  removeData.call(this, { setDatas, datas, index })
                }
                {...others}
              />
            ))
          ) : (
            <div></div>
          )}
        </RefreshPull>
      </div>
    </LocaleProvider>
  );
}
