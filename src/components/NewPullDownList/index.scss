.main-wrap,
.unload-list {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: #ebebeb;
}

.blank-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: calc(100vh - 83px);
  background: #fff;
  flex: 1;
  img {
    width: 180px;
    height: 180px;
    margin-top: 100px;
  }
  p {
    margin-top: 20px;
    font-family: PingFangSC-Regular, sans-serif;
    letter-spacing: 0;
    text-align: center;
    font-size: 16px;
    color: #333;
  }
}

.kymui-tabs {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.kymui-tabs__panel__item {
  flex: 1;
}

.new-pulldown-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.tab-header {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.kymui-tabs__container {
  display: flex;
  justify-content: stretch;
  flex: 1;
  height: 101%;
}

// 为新的RefreshPull组件添加样式，保持与原kymui Pull组件的兼容性
.new-pulldown-list-refresh-pull {
  flex: 1;
  height: 100%;

  .refresh-pull__scroll-container {
    height: 100%;
  }

  .refresh-pull__content {
    min-height: 100%;
  }

  .refresh-pull__body {
    min-height: 100%;
  }

  // 下拉刷新指示器样式优化
  .refresh-pull__refresh {
    background-color: #f8f8f8;
    border-bottom: 1px solid #e0e0e0;

    &-content {
      padding: 15px;
    }

    &-text {
      font-size: 14px;
      color: #666;
      margin-top: 8px;
    }
  }

  // 上拉加载指示器样式优化
  .refresh-pull__load {
    background-color: #f8f8f8;
    border-top: 1px solid #e0e0e0;

    &-content {
      padding: 15px;
    }

    &-text {
      font-size: 14px;
      color: #666;
      margin-top: 8px;
    }
  }

  // 加载动画样式
  .refresh-pull__loading-icon {
    width: 20px;
    height: 20px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #007aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 保持与原组件的兼容性
.new-pulldown-list {
  // 继承原来的ontheway-list样式
  .ontheway-list {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
  }
}
