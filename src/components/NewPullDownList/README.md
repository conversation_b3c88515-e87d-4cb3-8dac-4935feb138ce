# NewPullDownList 组件

这是一个新的下拉刷新列表组件，基于自定义的 RefreshPull 组件实现，替代了原来的 kymui Pull 组件。

## 特性

- ✅ 下拉刷新功能
- ✅ 上拉加载更多功能
- ✅ 与原 PullDownList 组件 API 完全兼容
- ✅ 支持自定义样式
- ✅ 支持多选下拉列表（根据用户偏好）
- ✅ 更好的触摸体验和动画效果

## 使用方法

### 基本用法

```javascript
import NewPullDownList from '../components/NewPullDownList';
import ListItem from './ListItem';

function MyComponent() {
  const loadData = ({ setLoading, setCurrentPage, setDatas, currentPage }) => {
    // 加载更多数据的逻辑
  };

  const refreshData = ({ setLoading, setCurrentPage, setDatas, setRefreshing }) => {
    // 刷新数据的逻辑
  };

  return (
    <NewPullDownList
      datas={[]}
      refreshing={0}
      loading={0}
      load={loadData}
      refresh={refreshData}
      item={ListItem}
      filter=""
      gotoDetail={() => {}}
    />
  );
}
```

### 替换原有的 PullDownList

只需要将导入语句从：
```javascript
import PullDownList from '../../components/PullDownList';
```

改为：
```javascript
import NewPullDownList from '../../components/NewPullDownList';
```

然后将组件名从 `<PullDownList>` 改为 `<NewPullDownList>`，其他 props 保持不变。

## Props

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| datas | Array | [] | 列表数据 |
| loading | Number | 0 | 加载状态 |
| refreshing | Number | 0 | 刷新状态 |
| currentPage | Number | 0 | 当前页码 |
| filter | String | '' | 过滤条件 |
| status | Number | 0 | 状态 |
| load | Function | () => {} | 加载更多回调 |
| refresh | Function | () => {} | 刷新回调 |
| orderData | Function | () => {} | 排序回调 |
| item | Component | null | 列表项组件 |
| error | Any | null | 错误信息 |
| visible | Function | () => {} | 可见性回调 |
| gotoDetail | Function | () => {} | 跳转详情回调 |
| zoneCode | String | '' | 区域代码 |
| order | Number | 0 | 排序方式 |
| tabValue | Number | 0 | 标签值 |
| removeData | Function | () => {} | 删除数据回调 |

## 与原组件的区别

1. **更好的触摸体验**：使用自定义的 RefreshPull 组件，提供更流畅的下拉刷新体验
2. **更好的动画效果**：优化了加载动画和过渡效果
3. **更好的样式控制**：提供了更多的样式定制选项
4. **完全兼容**：API 与原 PullDownList 组件完全一致，可以无缝替换

## 样式定制

可以通过覆盖以下 CSS 类来自定义样式：

- `.new-pulldown-list`: 主容器
- `.new-pulldown-list-refresh-pull`: RefreshPull 组件容器
- `.refresh-pull__refresh`: 下拉刷新指示器
- `.refresh-pull__load`: 上拉加载指示器
- `.refresh-pull__loading-icon`: 加载动画图标
