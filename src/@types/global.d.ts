// eslint-disable-next-line no-underscore-dangle
declare let _hmt: any; // 百度统计

// 丰图
declare namespace SFMap {
  function map(
    id: string,
    options: Record<string, any>,
  ): {
    setCenterAndZoom: (location: [number, number], scale: number) => void;
    remove: () => void;
  };

  function vectorLayer(
    options: Record<string, any>,
  ): {
    addLayer: (...arg: any[]) => void;
    removeLayer: (...arg: any[]) => void;
  };

  function circle(
    location: [number, number],
    options: Record<string, any>,
  ): any;

  function divIcon(options: Record<string, any>): any;

  class marker {
    constructor(location: [number, number], options: Record<string, any>): any;
  }
}

interface Window {
  __POWERED_BY_WUJIE__: boolean;
  __WUJIE_PUBLIC_PATH__: string;

  $wujie?: {
    bus: EventBus;
    shadowRoot?: ShadowRoot;
    props?: { [key: string]: any };
    location?: Record<string, unknown>;
  };
}
