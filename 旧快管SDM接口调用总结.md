# 旧快管 SDM 接口调用总结文档

## 概述
通过对代码库的全面搜索，发现项目中大量使用了以 `/sdm` 开头的接口，这些接口属于旧快管系统。本文档详细列出了所有调用情况，包括页面、场景和具体用途。

## 主要 SDM 服务分类

### 1. sdmCargoServices (货物服务)
**主要功能**: 货物管理、统计、监控相关接口

#### 1.1 首页/仪表盘相关
- **接口**: `/sdmCargoServices/dashBoardRest/getHomePageGoods`
- **页面**: 仪表盘首页 (`src/actions/dash-board.js`)
- **场景**: 获取仪表盘货物数据

#### 1.2 员工角色管理
- **接口**: `/sdmCargoServices/employee/searchEmpRole`
- **页面**: 全局登录验证 (`src/actions/index.js`)
- **场景**: 获取登录人角色信息

#### 1.3 货物流向管理
- **接口**: `/sdmCargoServices/allFlowGoodsRest/getAllFlowGoodsInfo`
- **页面**: 总货量页面 (`src/actions/index.js`)
- **场景**: 获取当前网点所有流向货物信息

- **接口**: `/sdmCargoServices/allFlowGoodsRest/getAllFlowGoodsInfoDetail`
- **页面**: 总货量详情页面 (`src/actions/index.js`)
- **场景**: 根据流向展示车辆和货物信息

#### 1.4 班前计划管理
- **接口**: `/sdmCargoServices/managerBoardRest/planArriveGoods`
- **页面**: 班前计划页面 (`src/actions/manage-board.js`)
- **场景**: 获取各班次预计到达量

#### 1.5 监控相关
- **接口**: `/sdmCargoServices/monitorRest/getMoreDepotAbility`
- **页面**: 效率详情页面 (`src/actions/index.js`)
- **场景**: 获取场地能力明细

- **接口**: `/sdmCargoServices/monitorRest/getUnloadingInfo`
- **页面**: 卸车效率页面 (`src/actions/index.js`)
- **场景**: 获取卸车效率详情

#### 1.6 统计管理
- **接口**: `/sdmCargoServices/statisGoodsRest/getZoneCodes`
- **页面**: 网点查询页面 (`src/actions/index.js`)
- **场景**: 查询所有网点信息

- **接口**: `/sdmCargoServices/statisGoodsAdminRest/getManagerLoadInfo`
- **页面**: 装车经理页面 (`src/actions/index.js`)
- **场景**: 获取装车数据列表

#### 1.7 尾货配置
- **接口**: `/sdmCargoServices/tFvpDepotConfig/selectAllZoneCode`
- **页面**: 尾货看板页面 (`src/actions/index.js`)
- **场景**: 获取始发地数据

### 2. sdmTransitServices (运输服务)
**主要功能**: 车辆运输、监控、调度相关接口

#### 2.1 首页数据
- **接口**: `/sdmTransitServices/transitUpgradeRestService/homePage`
- **页面**: 首页 (`src/actions/index.js`)
- **场景**: 获取首页数据

#### 2.2 车辆管理
- **接口**: `/sdmTransitServices/transitUpgradeRestService/vehicleStatistics`
- **页面**: 首页车辆统计 (`src/actions/dash-board.js`)
- **场景**: 首页车辆统计数据

- **接口**: `/sdmTransitServices/transitUpgradeRestService/vehicleDetail`
- **页面**: 车辆详情页面 (`src/actions/index.js`)
- **场景**: 获取车辆详情信息

- **接口**: `/sdmTransitServices/transitUpgradeRestService/vehicleMonitorDetail`
- **页面**: 车辆监控详情 (`src/actions/index.js`)
- **场景**: 获取已完成车辆详情

#### 2.3 搜索功能
- **接口**: `/sdmTransitServices/transitUpgradeRestService/mixSearch`
- **页面**: 车辆列表搜索页面 (`src/containers/VehicleList/SearchPage/index.js`)
- **场景**: 发车/到车二级页面搜索

#### 2.4 运力安排
- **接口**: `/sdmTransitServices/transitUpgradeRestService/getPlanArrange`
- **页面**: 运力安排页面 (`src/actions/resource-plan.js`)
- **场景**: 获取运力安排数据

#### 2.5 月台监控
- **接口**: `/sdmTransitServices/tMonitorInfo/getPlatformInfo`
- **页面**: 月台监控页面 (`src/actions/index.js`)
- **场景**: 月台监控数据

- **接口**: `/sdmTransitServices/tMonitorInfo/getUnloadPlatformInfo`
- **页面**: 卸车月台监控 (`src/actions/index.js`)
- **场景**: 卸车月台监控数据

- **接口**: `/sdmTransitServices/tMonitorInfo/getNewUnloadPlatformInfo`
- **页面**: 卸车调度月台状态 (`src/containers/UnloadCarScheduling/PlatformStatus/index.tsx`)
- **场景**: 查询网点卡口和车货信息

#### 2.6 地图轨迹
- **接口**: `/sdmTransitServices/gisMapTrace/getHisTrace`
- **页面**: 地图页面 (`src/actions/index.js`)
- **场景**: 获取历史轨迹数据

- **接口**: `/sdmTransitServices/gisMapTrace/getPlanTrace`
- **页面**: 地图页面 (`src/actions/index.js`)
- **场景**: 获取规划路线轨迹

#### 2.7 班次管理
- **接口**: `/sdmTransitServices/transitManageRestService/getManageDateList`
- **页面**: 班前计划页面 (`src/actions/manage-board.js`)
- **场景**: 获取管理日期列表

#### 2.8 车辆储备
- **接口**: `/sdmTransitServices/tMonitorInfo/genCarToolSum`
- **页面**: 班前计划页面 (`src/actions/manage-board.js`)
- **场景**: 获取车辆储备数据

#### 2.9 解密服务
- **接口**: `/sdmTransitServices/transitUpgradeRestService/getDecryptInfo`
- **页面**: 全局解密功能 (`src/actions/index.js`)
- **场景**: 手机号码加密解密

### 3. sdmCoreStaffServices (核心员工服务)
**主要功能**: 员工管理、值班调度、预警相关接口

#### 3.1 员工班次管理
- **接口**: `/sdmCoreStaffServices/transitShift/queryTransitEmpShiftList`
- **页面**: 用工情况搜索页面 (`src/containers/EmploymentSituation/SearchPage/index.tsx`)
- **场景**: 查询内部员工班次列表

- **接口**: `/sdmCoreStaffServices/transitShift/queryWaibaoTransitEmpShiftList`
- **页面**: 用工情况搜索页面 (`src/containers/EmploymentSituation/SearchPage/index.tsx`)
- **场景**: 查询外包员工班次列表

#### 3.2 员工信息查询
- **接口**: `/sdmCoreStaffServices/staffInfo/getStaffInfo`
- **页面**: 用户信息获取 (`src/utils/getUserInfo.ts`)
- **场景**: 获取用户真实姓名等信息

#### 3.3 值班调度
- **接口**: `/sdmCoreStaffServices/operationVisualizationDutyDispatchRest/selectEarlyWarning`
- **页面**: 消息预警页面 (`src/containers/newHome/Message/index.js`)
- **场景**: 预警查询

- **接口**: `/sdmCoreStaffServices/operationVisualizationDutyDispatchRest/detailEarlyWarning`
- **页面**: 预警详情页面 (`src/actions/index.js`)
- **场景**: 预警详情查询

- **接口**: `/sdmCoreStaffServices/operationVisualizationDutyDispatchRest/relieveEarlyWarning`
- **页面**: 预警处理页面 (`src/actions/index.js`)
- **场景**: 不再提醒/解除预警

#### 3.4 核心工作
- **接口**: `/sdmCoreStaffServices/operationVisualizationPostRest/selectCoreWork`
- **页面**: 核心工作页面 (`src/actions/index.js`)
- **场景**: 查询核心工作

#### 3.5 用工建议
- **接口**: `/sdmCoreStaffServices/workApprovalRest/selectWorkApprovalInfo`
- **页面**: 用工建议页面 (`src/actions/resource-plan.js`)
- **场景**: 获取用工建议

- **接口**: `/sdmCoreStaffServices/workApprovalRest/selectEfficiencyHistory`
- **页面**: 用工效能页面 (`src/actions/resource-plan.js`)
- **场景**: 获取用工效能历史数据

### 4. sdmBasicService (基础服务)
**主要功能**: 基础配置、平台信息、违规事件相关接口

#### 4.1 平台信息
- **接口**: `/sdmBasicService/platformInfo/selectPlatformInfoByDeptCode`
- **页面**: 月台号列表 (`src/actions/api.ts`)
- **场景**: 获取月台号列表

#### 4.2 网点配置
- **接口**: `/sdmBasicService/app/getFreightDeptCodeList`
- **页面**: 线路利润页面 (`src/actions/index.js`)
- **场景**: 获取货运网点编码列表

#### 4.3 违规事件管理
- **接口**: `/sdmBasicService/assistant/getVapdList`
- **页面**: VAPD违规事件列表 (`src/actions/index.js`)
- **场景**: 获取违规事件列表

- **接口**: `/sdmBasicService/assistant/getHistoryVapdSum`
- **页面**: VAPD历史汇总 (`src/actions/index.js`)
- **场景**: 获取历史违规事件汇总

- **接口**: `/sdmBasicService/assistant/getDetail`
- **页面**: VAPD事件详情 (`src/actions/index.js`)
- **场景**: 查询违规事件详情

- **接口**: `/sdmBasicService/assistant/getResponsibilityType`
- **页面**: VAPD责任归属 (`src/actions/index.js`)
- **场景**: 获取责任人归属配置

### 5. sdmNotesServices (笔记/报告服务)
**主要功能**: 巡场报告、消杀报告、巡检任务相关接口

#### 5.1 巡场报告
- **接口**: `/sdmNotesServices/batchTurnoverReport/submit`
- **页面**: 巡场报告提交 (`src/containers/CheckSitToolsNew/service.ts`)
- **场景**: 提交巡场报告

- **接口**: `/sdmNotesServices/batchTurnoverReport/createReport`
- **页面**: 巡场报告创建 (`src/containers/CheckSitToolsNew/service.ts`)
- **场景**: 新增巡场报告

- **接口**: `/sdmNotesServices/batchTurnoverReport/deleteReport/{id}`
- **页面**: 巡场报告删除 (`src/containers/CheckSitToolsNew/service.ts`)
- **场景**: 删除巡场报告

#### 5.2 消杀报告
- **接口**: `/sdmNotesServices/disinfectReport/getStatusByDate`
- **页面**: 巡场工具首页 (`src/containers/CheckSitTools/Home/index.js`)
- **场景**: 按日期查询网点消杀状态

- **接口**: `/sdmNotesServices/disinfectReport/listByReportDateAndBatch`
- **页面**: 消杀报告概览 (`src/containers/CheckSitTools/OverviewCardCVID19/index.js`)
- **场景**: 按批次查询网点消杀信息

#### 5.3 巡检任务
- **接口**: `/sdmNotesServices/tnPatrolTaskConfig/queryLikeDept`
- **页面**: 操作员巡检工具 (`src/containers/OperatorCheckTools/WarZoneDropdownAndZoneCodeSearch/index.tsx`)
- **场景**: 防抖搜索网点

- **接口**: `/sdmNotesServices/tnPatrolTask/queryAreaTaskNum`
- **页面**: 操作员巡检工具 (`src/containers/OperatorCheckTools/WarZoneDropdownAndZoneCodeSearch/index.tsx`)
- **场景**: 获取分拨区列表

- **接口**: `/sdmNotesServices/patrolTaskWeb/queryEmpByIdOrName`
- **页面**: 操作员巡检工具 (`src/containers/OperatorCheckTools/HandlerFullScreenSelect/index.tsx`)
- **场景**: 根据工号或姓名查询员工

### 6. sdmVideoServices (视频服务)
**主要功能**: 视频资源管理相关接口

#### 6.1 视频资源
- **接口**: `/sdmVideoServices/assetService/get-assets`
- **页面**: 视频列表 (`src/actions/index.js`)
- **场景**: 获取视频列表

#### 6.2 视频认证
- **接口**: `/sdmVideoServices/authService/getTempAkSk`
- **页面**: 视频客户端 (`src/utils/getVideoClient.ts`)
- **场景**: 获取临时凭证

### 7. sdmCollectService (收集服务)
**主要功能**: 数据收集、文件传输相关接口

#### 7.1 数据传输
- **接口**: `/sdmCollectService/daWangTrans/sendRequest`
- **页面**: 全局数据传输 (`src/utils/axios.js`)
- **场景**: 通过大王传输数据请求

- **接口**: `/sdmCollectService/daWangTrans/getFile`
- **页面**: 主题工具 (`src/utils/themTools.ts`)
- **场景**: 获取文件资源

## 代理配置状态
在 `src/proxyList.js` 中，所有 SDM 相关服务都被注释掉了：
```javascript
// 'sdmTransitServices',
// 'sdmCargoServices', 
// 'sdmBasicService',
// 'sdmCollectService',
// 'sdmCoreStaffServices',
// 'sdmEqptService',
// 'sdmNotesServices',
// 'sdmVideoServices',
```

## 总结
项目中大量使用了旧快管的 SDM 接口，涉及：
- **7个主要服务**: sdmCargoServices、sdmTransitServices、sdmCoreStaffServices、sdmBasicService、sdmNotesServices、sdmVideoServices、sdmCollectService
- **50+个具体接口**: 覆盖货物管理、车辆运输、员工管理、基础配置、报告管理、视频服务等
- **30+个页面组件**: 从首页到各种详情页面都有涉及
- **核心功能**: 首页数据、车辆监控、员工管理、月台监控、巡场报告、预警管理等

这些接口需要逐步迁移到新的服务架构中。
