# 人员效能完整样式优化实施计划

## 项目概述

基于截图分析，人员效能模块需要进行全面的样式优化，以匹配设计稿的视觉效果。本文档提供完整的实施计划和技术方案。

## 优化范围

### 涉及文件清单

```
src/containers/newHome/NewManageBoard/PersonnelEfficiency/
├── index.scss                     # 主容器样式
├── index.js                       # 主组件
├── EfficiencyCards/
│   ├── index.scss                 # 效率卡片样式 ⭐ 重点优化
│   └── index.js                   # 效率卡片组件
└── GroupEfficiencyTable/
    └── index.scss                 # 表格样式

src/containers/newHome/NewManageBoard/components/
├── LineTitle/index.scss           # 标题组件样式
└── LineChart/index.scss           # 图表组件样式
```

## 分阶段实施计划

### 第一阶段：效率卡片区域优化 ⭐ 当前焦点

#### 目标效果

- 更温和的卡片背景效果
- 清晰的信息层次结构
- 优化的字体大小和颜色
- 改进的布局和间距

#### 具体实施内容

**1. 容器样式优化**

```scss
.EfficiencyCards-root {
  margin: 8px 16px 16px 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 18px 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
```

**2. 网格布局优化**

```scss
.cards-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  align-items: start;
}
```

**3. 卡片内容样式优化**

```scss
.efficiency-card {
  text-align: center;
  padding: 12px 6px 8px 6px;

  .card-title {
    font-size: 11px;
    color: #666;
    margin-bottom: 10px;
    line-height: 1.3;
    font-weight: 400;
    word-break: keep-all;
  }

  .card-content {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 6px;

    .card-number {
      font-size: 20px;
      font-weight: 700;
      color: #333;
      line-height: 1;
    }

    .card-unit {
      font-size: 12px;
      color: #666;
      margin-left: 2px;
      font-weight: 500;
    }

    .card-arrow {
      font-size: 12px;
      color: #ff6a17;
      margin-left: 4px;
      transform: translateY(-2px);
    }
  }

  .card-desc {
    font-size: 10px;
    color: #ff6a17;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.2;

    .desc-arrow {
      margin-left: 2px;
      font-size: 10px;
    }
  }
}
```

### 第二阶段：整体容器和布局优化

#### 目标效果

- 调整主容器背景色为温和的米黄色调
- 优化各模块间的间距和布局
- 统一圆角和阴影效果

#### 具体实施内容

**1. 主容器背景优化**

```scss
.PersonnelEfficiency-root {
  padding: 0;
  background: linear-gradient(180deg, #f5f0e8 0%, #fdfcf8 100%);
  min-height: 100vh;
}
```

**2. 标题区域优化**

```scss
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 16px;
  padding-top: 8px;

  .LineTitle-root {
    margin: 16px 0 8px 0;
    flex: 1;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .date-selector {
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    color: #333;
    backdrop-filter: blur(5px);

    .placeholder {
      color: #999;
    }
  }
}
```

### 第三阶段：图表区域样式优化

#### 目标效果

- 优化图表容器的背景和边框
- 改进图例的显示效果
- 调整图表标题和说明文字

#### 具体实施内容

**1. 图表容器优化**

```scss
.LineChart-root {
  margin: 12px 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  padding: 20px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);

  .top-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
  }

  .tooltip {
    margin-bottom: 12px;

    &__content {
      display: flex;
      align-items: center;
      margin: 4px 0;
      font-size: 12px;
      color: #666;

      i {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 8px;
        border-radius: 2px;

        &.radio {
          border-radius: 50%;
        }

        &.square {
          border-radius: 2px;
        }
      }

      span {
        margin-left: 8px;
        font-weight: 600;
        color: #333;
      }
    }
  }

  .canvas-container {
    position: relative;
    height: 200px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 8px;
    padding: 8px;

    canvas {
      width: 100%;
      height: 100%;
    }
  }
}
```

### 第四阶段：标题组件优化

#### 目标效果

- 调整红色装饰线条的视觉效果
- 优化标题字体和间距
- 改进整体视觉层次

#### 具体实施内容

**1. LineTitle 组件优化**

```scss
.LineTitle-root {
  font-size: 16px;
  font-weight: 600;
  line-height: 22.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto 16px auto;
  color: #333;

  &::before,
  &::after {
    width: 32px;
    height: 3px;
    content: '';
    background: linear-gradient(
      270deg,
      #cc1b23 0%,
      rgba(204, 27, 35, 0.3) 100%
    );
    border-radius: 1.5px;
  }

  &::before {
    margin-right: 10px;
  }

  &::after {
    margin-left: 10px;
    transform: rotate(180deg);
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    white-space: nowrap;
  }
}
```

### 第五阶段：响应式和兼容性优化

#### 具体实施内容

**1. 移动端适配**

```scss
@media (max-width: 375px) {
  .PersonnelEfficiency-root {
    .header-section {
      margin: 0 12px;

      .date-selector {
        padding: 6px 10px;
        font-size: 13px;
      }
    }
  }

  .EfficiencyCards-root {
    margin: 8px 12px 16px 12px;
    padding: 16px 12px;

    .cards-container {
      gap: 8px;
    }

    .efficiency-card {
      padding: 10px 4px 8px 4px;

      .card-title {
        font-size: 10px;
        margin-bottom: 8px;
      }

      .card-content .card-number {
        font-size: 18px;
      }
    }
  }

  .LineChart-root {
    margin: 12px 12px;
    padding: 16px 12px;
  }
}

@media (max-width: 320px) {
  .EfficiencyCards-root {
    .cards-container {
      gap: 6px;
    }

    .efficiency-card {
      padding: 8px 2px 6px 2px;

      .card-title {
        font-size: 9px;
      }

      .card-content .card-number {
        font-size: 16px;
      }
    }
  }
}
```

## 实施流程

### 开发流程

1. **切换到 Code 模式**：由于 Architect 模式只能编辑 Markdown 文件
2. **按阶段实施**：从效率卡片开始，逐步完成所有优化
3. **测试验证**：每个阶段完成后进行测试
4. **调整优化**：根据实际效果进行微调

### 测试计划

1. **视觉测试**

   - Chrome DevTools 设备模拟
   - 真机测试（iOS/Android）
   - 不同屏幕尺寸验证

2. **性能测试**

   - backdrop-filter 性能影响
   - 渲染性能检测
   - 内存使用情况

3. **兼容性测试**
   - 主流浏览器兼容性
   - 低端设备性能测试
   - WebView 环境测试

## 技术要点

### CSS 关键技术

- **backdrop-filter**: 实现毛玻璃效果
- **CSS Grid**: 灵活的卡片布局
- **渐变背景**: 营造层次感
- **响应式设计**: 移动端适配

### 注意事项

1. **性能考虑**

   - backdrop-filter 在低端设备可能有性能问题
   - 需要提供降级方案

2. **兼容性处理**

   - 老版本浏览器的兼容
   - 不同 WebView 的表现

3. **维护性**
   - 使用 CSS 变量便于主题切换
   - 保持代码结构清晰

## 预期效果

### 视觉提升

- ✅ 更现代、温和的视觉效果
- ✅ 清晰的信息层次和可读性
- ✅ 统一协调的设计语言
- ✅ 优雅的毛玻璃质感

### 用户体验提升

- ✅ 更好的移动端适配
- ✅ 更流畅的视觉过渡
- ✅ 更直观的数据展示
- ✅ 更一致的交互体验

### 技术提升

- ✅ 现代 CSS 技术应用
- ✅ 响应式设计最佳实践
- ✅ 性能和兼容性平衡
- ✅ 可维护的代码结构

## 下一步行动

1. **立即执行**：切换到 Code 模式开始实施第一阶段优化
2. **验证效果**：在本地环境测试优化效果
3. **迭代改进**：根据实际效果进行调整
4. **完善文档**：更新实施过程中的发现和改进
