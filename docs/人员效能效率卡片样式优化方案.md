# 人员效能效率卡片样式优化方案

## 项目背景

当前人员效能模块中的效率卡片样式需要优化，以更好地匹配设计稿的视觉效果。根据截图分析，需要对以下方面进行改进：

## 当前问题分析

### 1. 视觉问题

- 卡片背景色过于突出，需要更温和的效果
- 圆角和阴影效果需要调整
- 字体层次不够清晰

### 2. 布局问题

- 四个卡片间距需要优化
- 内容对齐方式需要调整
- 数值和单位的显示效果需要改进

### 3. 色彩问题

- 日环比的橙色需要调整
- 整体配色需要与米黄色背景协调

## 详细优化方案

### 文件路径

- 主要文件：`src/containers/newHome/NewManageBoard/PersonnelEfficiency/EfficiencyCards/index.scss`
- 组件文件：`src/containers/newHome/NewManageBoard/PersonnelEfficiency/EfficiencyCards/index.js`

### 1. 容器样式优化

```scss
.EfficiencyCards-root {
  margin: 8px 16px 16px 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 18px 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
```

**优化要点：**

- 使用半透明白色背景提升层次感
- 增加 backdrop-filter 实现毛玻璃效果
- 调整圆角为 12px，更现代化
- 优化阴影效果，更自然

### 2. 网格布局优化

```scss
.cards-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  align-items: start;
}
```

**优化要点：**

- 调整间距为 12px，更合适的视觉间距
- 使用 align-items: start 确保对齐一致

### 3. 单个卡片样式优化

```scss
.efficiency-card {
  text-align: center;
  padding: 12px 6px 8px 6px;

  .card-title {
    font-size: 11px;
    color: #666;
    margin-bottom: 10px;
    line-height: 1.3;
    font-weight: 400;
    word-break: keep-all;
  }

  .card-content {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 6px;

    .card-number {
      font-size: 20px;
      font-weight: 700;
      color: #333;
      line-height: 1;
    }

    .card-unit {
      font-size: 12px;
      color: #666;
      margin-left: 2px;
      font-weight: 500;
    }

    .card-arrow {
      font-size: 12px;
      color: #ff6a17;
      margin-left: 4px;
      transform: translateY(-2px);
    }
  }

  .card-desc {
    font-size: 10px;
    color: #ff6a17;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.2;

    .desc-arrow {
      margin-left: 2px;
      font-size: 10px;
    }
  }
}
```

**优化要点：**

- 标题字体调整为 11px，更协调
- 数值字体加粗到 700，突出重要性
- 单位字体调整，与数值形成层次
- 日环比箭头位置微调，视觉效果更佳

### 4. 响应式优化

```scss
@media (max-width: 375px) {
  .EfficiencyCards-root {
    margin: 8px 12px 16px 12px;
    padding: 16px 12px;

    .cards-container {
      gap: 8px;
    }

    .efficiency-card {
      padding: 10px 4px 8px 4px;

      .card-title {
        font-size: 10px;
        margin-bottom: 8px;
      }

      .card-content .card-number {
        font-size: 18px;
      }
    }
  }
}
```

**优化要点：**

- 小屏幕下调整边距和字体
- 确保在所有设备上都有良好显示

## 实施步骤

### 第一步：基础样式优化

1. 更新 `.EfficiencyCards-root` 容器样式
2. 调整背景、圆角、阴影效果
3. 测试视觉效果

### 第二步：布局和间距优化

1. 优化 `.cards-container` 网格布局
2. 调整卡片间距
3. 优化内边距设置

### 第三步：字体和颜色优化

1. 调整各级标题字体大小
2. 优化数值显示效果
3. 调整日环比颜色和位置

### 第四步：响应式和兼容性

1. 添加小屏幕适配
2. 测试不同设备显示效果
3. 确保兼容性

## 预期效果

### 视觉提升

- 更温和、现代的卡片效果
- 更清晰的信息层次
- 更协调的整体配色

### 用户体验提升

- 更好的可读性
- 更一致的视觉体验
- 更好的移动端适配

## 测试要点

1. **视觉测试**

   - 检查卡片背景透明度效果
   - 验证圆角和阴影是否自然
   - 确认字体层次是否清晰

2. **布局测试**

   - 检查四个卡片是否对齐
   - 验证间距是否合适
   - 确认响应式效果

3. **兼容性测试**
   - 测试不同屏幕尺寸
   - 检查各种移动设备
   - 验证不同浏览器效果

## 注意事项

1. **颜色一致性**

   - 确保与整体页面配色协调
   - 保持品牌色彩规范

2. **性能考虑**

   - backdrop-filter 可能影响性能
   - 需要考虑低端设备兼容性

3. **可维护性**
   - 使用 CSS 变量便于后续调整
   - 保持代码结构清晰

## 后续优化方向

1. **动画效果**

   - 可以考虑添加 subtle 的 hover 效果
   - 数值变化时的过渡动画

2. **交互优化**

   - 考虑添加点击反馈
   - 优化触摸体验

3. **数据可视化**
   - 可以考虑添加小型图表
   - 趋势指示器等
