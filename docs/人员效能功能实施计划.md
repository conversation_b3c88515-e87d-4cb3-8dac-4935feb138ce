# 人员效能功能实施计划

## 项目背景

根据设计稿要求，在现有管理看板中新增"人员效能"tab，实现人员效率数据的展示功能。

## 技术分析

### 现有资源

- **日期组件**: 复用 `src/components/DateSelect/index.tsx`
- **图表组件**: 复用 `src/containers/newHome/NewManageBoard/components/LineChart/index.js`
- **卡片组件**: 复用 `src/containers/newHome/NewManageBoard/components/OverviewCard/index.js`
- **标题组件**: 复用 `src/containers/newHome/NewManageBoard/components/LineTitle/index.js`

### 设计稿功能要求

1. **人效趋势 (T-1)** 模块

   - 日期选择器 (默认显示: 2020-08-09)
   - 四个数据卡片:
     - 日操作效率: 540↑ (日环比: 1.2%↑)
     - 操作劳效: 53.1
     - 移动劳效: 65.9
     - 在职劳效: 25.8

2. **图表展示** 模块

   - 货量(吨) 和 在职劳效 趋势图
   - 操作出勤人数、移动出勤人数、出勤率 图表

3. **各组别累计人效比 (T-1)** 模块
   - 显示各组别的详细数据

## 实施步骤

### 第一步: 修改主框架 (NewManageBoard)

- 文件: `src/containers/newHome/NewManageBoard/index.js`
- 修改内容:
  - 更新 `TAB_INDICES` 常量
  - 修改 `tabsList` 数组，增加"人员效能"tab
  - 更新组件渲染逻辑

### 第二步: 创建人员效能主组件

- 文件: `src/containers/newHome/NewManageBoard/PersonnelEfficiency/index.js`
- 文件: `src/containers/newHome/NewManageBoard/PersonnelEfficiency/index.scss`

### 第三步: 创建子组件

1. **日期选择器**: 基于现有 `DateSelect` 组件封装
2. **效率卡片**: 基于现有 `OverviewCard` 组件
3. **组别表格**: 新建 `GroupEfficiencyTable` 组件

### 第四步: 实现样式

- 遵循现有设计规范
- 使用浅黄色背景 (#FFF7E6 类似色)
- 保持与"班中监控"一致的视觉风格

## 组件结构

```
NewManageBoard/
├── index.js (修改: 增加人员效能tab)
├── PersonnelEfficiency/ (新建)
│   ├── index.js
│   ├── index.scss
│   └── GroupEfficiencyTable/
│       ├── index.js
│       └── index.scss
├── Monitor/ (保持不变)
└── components/ (复用现有)
```

## 数据结构设计

### 效率卡片数据

```javascript
const efficiencyCards = [
  {
    title: '日操作效率',
    value: 540,
    unit: '↑',
    trend: { value: 1.2, direction: 'up', label: '日环比' },
  },
  // ...其他卡片
];
```

### 图表数据

```javascript
const chartData = [
  {
    name: 'volume', // 货量
    time: '12-27',
    value: 1000,
    value2: 50, // 对应在职劳效
  },
  // ...更多数据点
];
```

## 技术实现要点

1. **Tab 切换**: 使用现有的 antd-mobile Tabs 组件
2. **状态管理**: 采用 useReducer 管理组件状态
3. **图表渲染**: 复用 F2 图表库的实现
4. **响应式设计**: 保持移动端适配
5. **数据模拟**: 使用静态数据，不涉及 API 调用

## 验收标准

- ✅ 新增"人员效能"tab 且功能正常
- ✅ 保持"班中监控"功能不受影响
- ✅ 日期选择器功能正常
- ✅ 四个效率数据卡片显示正确
- ✅ 两个图表渲染正常
- ✅ 组别效率表格展示完整
- ✅ 样式与设计稿一致
- ✅ 移动端适配良好
